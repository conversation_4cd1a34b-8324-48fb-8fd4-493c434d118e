import React from 'react';
import { Link, router } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { setPageTitle } from '../../store/themeConfigSlice';
import { useDispatch } from 'react-redux';

const ComingSoonBoxed = () => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Coming Soon Boxed'));
    });
    const [demo1, setDemo1] = useState<any>({ days: null, hours: null, minutes: null, seconds: null });
    const [timer1, setTimer1] = useState<any>(null);

    const setTimerDemo1 = () => {
        const date = new Date();
        date.setFullYear(date.getFullYear() + 1);
        const countDownDate = date.getTime();

        let updatedValue: any = {};
        setTimer1(
            setInterval(() => {
                const now = new Date().getTime();
                const distance = countDownDate - now;
                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                updatedValue = { days, hours, minutes, seconds };
                setDemo1(updatedValue);

                if (distance < 0) {
                    clearInterval(timer1);
                    setDemo1({ days: 0, hours: 0, minutes: 0, seconds: 0 });
                }
            }, 1000)
        );
    };

    useEffect(() => {
        setTimerDemo1();
        return () => {
            clearInterval(timer1);
        };
    }, []);

    const submitForm = () => {
        router.visit('/');
    };

    return (
        <div>
            <div className="absolute inset-0">
                <img src="/assets/images/auth/bg-gradient.png" alt="image" className="w-full h-full object-cover" />
            </div>
            <div className="relative flex justify-center items-center bg-[url(/assets/images/auth/map.png)] dark:bg-[#060818] bg-cover bg-no-repeat bg-center px-6 sm:px-16 py-10 min-h-screen">
                <img src="/assets/images/auth/coming-soon-object1.png" alt="image" className="top-1/2 left-0 absolute h-full max-h-[893px] -translate-y-1/2" />
                <img src="/assets/images/auth/coming-soon-object2.png" alt="image" className="top-0 left-24 md:left-[30%] absolute h-40" />
                <img src="/assets/images/auth/coming-soon-object3.png" alt="image" className="top-0 right-0 absolute h-[300px]" />
                <img src="/assets/images/auth/polygon-object.svg" alt="image" className="bottom-0 absolute end-[28%]" />
                <div className="relative bg-[linear-gradient(45deg,#fff9f9_0%,rgba(255,255,255,0)_25%,rgba(255,255,255,0)_75%,_#fff9f9_100%)] dark:bg-[linear-gradient(52.22deg,#0E1726_0%,rgba(14,23,38,0)_18.66%,rgba(14,23,38,0)_51.04%,rgba(14,23,38,0)_80.07%,#0E1726_100%)] p-2 rounded-md w-full max-w-[870px] text-center">
                    <div className="bg-white/60 dark:bg-black/50 backdrop-blur-lg p-4 sm:p-6 rounded-md">
                        <div className="mx-auto mt-5 md:mt-16 w-full max-w-[550px]">
                            <div className="mb-12">
                                <h1 className="font-extrabold text-3xl text-primary md:text-4xl uppercase !leading-snug">Coming Soon</h1>
                                <p className="font-bold text-base text-white-dark leading-normal">We will be here in a shortwhile.....</p>
                            </div>
                            <div
                                className="flex justify-center items-center gap-2 md:gap-4 mb-16 md:mb-24 font-bold text-primary text-xl sm:text-2xl md:text-[50px] leading-none"
                            >
                                <div className="inline-flex relative justify-center items-center bg-primary-light p-2 rounded-md w-14 sm:w-16 md:min-w-[120px] h-12 sm:h-16 md:h-24">
                                    <div className="absolute inset-1 flex flex-col gap-1">
                                        <span className="bg-primary/[12%] rounded-md w-full h-full"></span>
                                        <span className="bg-white rounded-md w-full h-full"></span>
                                    </div>
                                    <span className="relative">{demo1.days}</span>
                                </div>
                                <span>:</span>
                                <div className="inline-flex relative justify-center items-center bg-primary-light p-2 rounded-md w-12 sm:w-16 md:min-w-[96px] h-12 sm:h-16 md:h-24">
                                    <div className="absolute inset-1 flex flex-col gap-1">
                                        <span className="bg-primary/[12%] rounded-md w-full h-full"></span>
                                        <span className="bg-white rounded-md w-full h-full"></span>
                                    </div>
                                    <span className="relative">{demo1.hours}</span>
                                </div>
                                <span>:</span>
                                <div className="inline-flex relative justify-center items-center bg-primary-light p-2 rounded-md w-12 sm:w-16 md:min-w-[96px] h-12 sm:h-16 md:h-24">
                                    <div className="absolute inset-1 flex flex-col gap-1">
                                        <span className="bg-primary/[12%] rounded-md w-full h-full"></span>
                                        <span className="bg-white rounded-md w-full h-full"></span>
                                    </div>
                                    <span className="relative">{demo1.minutes}</span>
                                </div>
                                <span>:</span>
                                <div className="inline-flex relative justify-center items-center bg-primary-light p-2 rounded-md w-12 sm:w-16 md:min-w-[96px] h-12 sm:h-16 md:h-24">
                                    <div className="absolute inset-1 flex flex-col gap-1">
                                        <span className="bg-primary/[12%] rounded-md w-full h-full"></span>
                                        <span className="bg-white rounded-md w-full h-full"></span>
                                    </div>
                                    <span className="relative">{demo1.seconds}</span>
                                </div>
                            </div>
                           
                            <p className="dark:text-white">© {new Date().getFullYear()}.  All Rights Reserved.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ComingSoonBoxed;

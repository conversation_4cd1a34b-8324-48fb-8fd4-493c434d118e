import { Link, router } from '@inertiajs/react';
import { useDispatch, useSelector } from 'react-redux';
import { IRootState } from '../../store';
import { setPageTitle, toggleRTL } from '../../store/themeConfigSlice';
import { useEffect, useState } from 'react';
import Dropdown from '../../components/Dropdown';
import i18next from 'i18next';

const RegisterBoxed = () => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Register Boxed'));
    });
    const isDark = useSelector((state: IRootState) => state.themeConfig.theme === 'dark' || state.themeConfig.isDarkMode);
    const isRtl = useSelector((state: IRootState) => state.themeConfig.rtlClass) === 'rtl' ? true : false;
    const themeConfig = useSelector((state: IRootState) => state.themeConfig);
    const centralDomain = import.meta.env.VITE_CENTRAL_DOMAIN || 'booknblush.com';

    const setLocale = (flag: string) => {
        setFlag(flag);
        if (flag.toLowerCase() === 'ae') {
            dispatch(toggleRTL('rtl'));
        } else {
            dispatch(toggleRTL('ltr'));
        }
    };
    const [flag, setFlag] = useState(themeConfig.locale);

    const [formData, setFormData] = useState({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'customer', // Default role
        company_name: '',
        company_domain: '',
    });

    const [showVendorFields, setShowVendorFields] = useState(false);

    const handleChange = (e) => {
        const { id, value, type, checked } = e.target;
        const newValue = type === 'checkbox' ? checked : value;

        setFormData(prev => ({
            ...prev,
            [id]: newValue
        }));

        // Show/hide vendor fields when role changes
        if (id === 'role') {
            setShowVendorFields(value === 'vendor');
        }
    };

    const setRole = (role) => {
        setFormData(prev => ({
            ...prev,
            role
        }));
        setShowVendorFields(role === 'vendor');
    };

    const submitForm = (e) => {
        e.preventDefault();
        router.post('/auth/register', formData);
    };

    return (
        <div>
            <div className="absolute inset-0">
                <img src="/assets/images/auth/bg-gradient.png" alt="image" className="w-full h-full object-cover" />
            </div>

            <div className="relative flex justify-center items-center bg-[url(/assets/images/auth/map.png)] dark:bg-[#060818] bg-cover bg-no-repeat bg-center px-6 sm:px-16 py-10 min-h-screen">
                <img src="/assets/images/auth/coming-soon-object1.png" alt="image" className="top-1/2 left-0 absolute h-full max-h-[893px] -translate-y-1/2" />
                <img src="/assets/images/auth/coming-soon-object2.png" alt="image" className="top-0 left-24 md:left-[30%] absolute h-40" />
                <img src="/assets/images/auth/coming-soon-object3.png" alt="image" className="top-0 right-0 absolute h-[300px]" />
                <img src="/assets/images/auth/polygon-object.svg" alt="image" className="bottom-0 absolute end-[28%]" />
                <div className="relative bg-[linear-gradient(45deg,#fff9f9_0%,rgba(255,255,255,0)_25%,rgba(255,255,255,0)_75%,_#fff9f9_100%)] dark:bg-[linear-gradient(52.22deg,#0E1726_0%,rgba(14,23,38,0)_18.66%,rgba(14,23,38,0)_51.04%,rgba(14,23,38,0)_80.07%,#0E1726_100%)] p-2 rounded-md w-full max-w-[870px]">
                    <div className="relative flex flex-col justify-center bg-white/60 dark:bg-black/50 backdrop-blur-lg px-6 py-10 rounded-md lg:min-h-[758px]">

                        <div className="mx-auto px-10 max-md:px-4 w-full">
                            <div className="mb-10">
                                <h1 className="font-extrabold text-3xl text-primary md:text-4xl uppercase !leading-snug">Sign Up</h1>
                                <p className="font-bold text-base text-white-dark leading-normal">Enter your details to create an account</p>
                            </div>

                            {/* Role Selection Cards */}
                            <div className="mb-8">
                                <label className="block mb-3 font-semibold">I want to register as:</label>
                                <div className="gap-4 grid grid-cols-2">
                                    <div
                                        className={`cursor-pointer rounded-lg p-4 border-2 transition-all duration-300 flex flex-col items-center ${formData.role === 'customer'
                                            ? 'border-primary bg-primary/10 text-primary'
                                            : 'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600'
                                            }`}
                                        onClick={() => setRole('customer')}
                                    >
                                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="8" r="4" fill="currentColor" />
                                            <path d="M20 19C20 16.7909 16.4183 15 12 15C7.58172 15 4 16.7909 4 19V21H20V19Z" fill="currentColor" />
                                        </svg>
                                        <span className="mt-2 font-semibold">Customer</span>
                                    </div>

                                    <div
                                        className={`cursor-pointer rounded-lg p-4 border-2 transition-all duration-300 flex flex-col items-center ${formData.role === 'vendor'
                                            ? 'border-primary bg-primary/10 text-primary'
                                            : 'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600'
                                            }`}
                                        onClick={() => setRole('vendor')}
                                    >
                                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 11.5V8.8C21 7.11984 21 6.27976 20.673 5.63803C20.3854 5.07354 19.9265 4.6146 19.362 4.32698C18.7202 4 17.8802 4 16.2 4H7.8C6.11984 4 5.27976 4 4.63803 4.32698C4.07354 4.6146 3.6146 5.07354 3.32698 5.63803C3 6.27976 3 7.11984 3 8.8V17.2C3 18.8802 3 19.7202 3.32698 20.362C3.6146 20.9265 4.07354 21.3854 4.63803 21.673C5.27976 22 6.11984 22 7.8 22H12.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M3 8H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M7 4V2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M17 4V2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M18 14L16 16L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            <circle cx="18" cy="18" r="4" stroke="currentColor" strokeWidth="2" />
                                        </svg>
                                        <span className="mt-2 font-semibold">Vendor</span>
                                    </div>
                                </div>
                            </div>

                            <form className="space-y-5 dark:text-white" onSubmit={submitForm}>
                                <div className="gap-5 grid grid-cols-2">
                                    <div>
                                        <label htmlFor="name">Name</label>
                                        <div className="relative text-white-dark">
                                            <input
                                                id="name"
                                                type="text"
                                                placeholder="Enter Name"
                                                className="placeholder:text-white-dark form-input ps-10"
                                                value={formData.name}
                                                onChange={handleChange}
                                                required
                                            />
                                            <span className="top-1/2 absolute -translate-y-1/2 start-4">
                                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                    <circle cx="9" cy="4.5" r="3" fill="#888EA8" />
                                                    <path
                                                        opacity="0.5"
                                                        d="M15 13.125C15 14.989 15 16.5 9 16.5C3 16.5 3 14.989 3 13.125C3 11.261 5.68629 9.75 9 9.75C12.3137 9.75 15 11.261 15 13.125Z"
                                                        fill="#888EA8"
                                                    />
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <label htmlFor="email">Email</label>
                                        <div className="relative text-white-dark">
                                            <input
                                                id="email"
                                                type="email"
                                                placeholder="Enter Email"
                                                className="placeholder:text-white-dark form-input ps-10"
                                                value={formData.email}
                                                onChange={handleChange}
                                                required
                                            />
                                            <span className="top-1/2 absolute -translate-y-1/2 start-4">
                                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                    <path
                                                        opacity="0.5"
                                                        d="M10.65 2.25H7.35C4.23873 2.25 2.6831 2.25 1.71655 3.23851C0.75 4.22703 0.75 5.81802 0.75 9C0.75 12.182 0.75 13.773 1.71655 14.7615C2.6831 15.75 4.23873 15.75 7.35 15.75H10.65C13.7613 15.75 15.3169 15.75 16.2835 14.7615C17.25 13.773 17.25 12.182 17.25 9C17.25 5.81802 17.25 4.22703 16.2835 3.23851C15.3169 2.25 13.7613 2.25 10.65 2.25Z"
                                                        fill="#888EA8"
                                                    />
                                                    <path
                                                        d="M14.3465 6.02574C14.609 5.80698 14.6445 5.41681 14.4257 5.15429C14.207 4.89177 13.8168 4.8563 13.5543 5.07507L11.7732 6.55931C11.0035 7.20072 10.4691 7.6446 10.018 7.93476C9.58125 8.21564 9.28509 8.30993 9.00041 8.30993C8.71572 8.30993 8.41956 8.21564 7.98284 7.93476C7.53168 7.6446 6.9973 7.20072 6.22761 6.55931L4.44652 5.07507C4.184 4.8563 3.79384 4.89177 3.57507 5.15429C3.3563 5.41681 3.39177 5.80698 3.65429 6.02574L5.4664 7.53583C6.19764 8.14522 6.79033 8.63914 7.31343 8.97558C7.85834 9.32604 8.38902 9.54743 9.00041 9.54743C9.6118 9.54743 10.1425 9.32604 10.6874 8.97558C11.2105 8.63914 11.8032 8.14522 12.5344 7.53582L14.3465 6.02574Z"
                                                        fill="#888EA8"
                                                    />
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <label htmlFor="password">Password</label>
                                        <div className="relative text-white-dark">
                                            <input
                                                id="password"
                                                type="password"
                                                placeholder="Enter Password"
                                                className="placeholder:text-white-dark form-input ps-10"
                                                value={formData.password}
                                                onChange={handleChange}
                                                required
                                            />
                                            <span className="top-1/2 absolute -translate-y-1/2 start-4">
                                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                    <path
                                                        opacity="0.5"
                                                        d="M1.5 12C1.5 9.87868 1.5 8.81802 2.15901 8.15901C2.81802 7.5 3.87868 7.5 6 7.5H12C14.1213 7.5 15.182 7.5 15.841 8.15901C16.5 8.81802 16.5 9.87868 16.5 12C16.5 14.1213 16.5 15.182 15.841 15.841C15.182 16.5 14.1213 16.5 12 16.5H6C3.87868 16.5 2.81802 16.5 2.15901 15.841C1.5 15.182 1.5 14.1213 1.5 12Z"
                                                        fill="currentColor"
                                                    />
                                                    <path
                                                        d="M6 12.75C6.41421 12.75 6.75 12.4142 6.75 12C6.75 11.5858 6.41421 11.25 6 11.25C5.58579 11.25 5.25 11.5858 5.25 12C5.25 12.4142 5.58579 12.75 6 12.75Z"
                                                        fill="currentColor"
                                                    />
                                                    <path
                                                        d="M9 12.75C9.41421 12.75 9.75 12.4142 9.75 12C9.75 11.5858 9.41421 11.25 9 11.25C8.58579 11.25 8.25 11.5858 8.25 12C8.25 12.4142 8.58579 12.75 9 12.75Z"
                                                        fill="currentColor"
                                                    />
                                                    <path
                                                        d="M12.75 12C12.75 12.4142 12.4142 12.75 12 12.75C11.5858 12.75 11.25 12.4142 11.25 12C11.25 11.5858 11.5858 11.25 12 11.25C12.4142 11.25 12.75 11.5858 12.75 12Z"
                                                        fill="currentColor"
                                                    />
                                                    <path
                                                        d="M5.0625 6C5.0625 3.82538 6.82538 2.0625 9 2.0625C11.1746 2.0625 12.9375 3.82538 12.9375 6V7.50268C13.363 7.50665 13.7351 7.51651 14.0625 7.54096V6C14.0625 3.20406 11.7959 0.9375 9 0.9375C6.20406 0.9375 3.9375 3.20406 3.9375 6V7.54096C4.26488 7.51651 4.63698 7.50665 5.0625 7.50268V6Z"
                                                        fill="currentColor"
                                                    />
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <label htmlFor="password_confirmation">Confirm Password</label>
                                        <div className="relative text-white-dark">
                                            <input
                                                id="password_confirmation"
                                                type="password"
                                                placeholder="Confirm Password"
                                                className="placeholder:text-white-dark form-input ps-10"
                                                value={formData.password_confirmation}
                                                onChange={handleChange}
                                                required
                                            />
                                            <span className="top-1/2 absolute -translate-y-1/2 start-4">
                                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                    <path
                                                        opacity="0.5"
                                                        d="M1.5 12C1.5 9.87868 1.5 8.81802 2.15901 8.15901C2.81802 7.5 3.87868 7.5 6 7.5H12C14.1213 7.5 15.182 7.5 15.841 8.15901C16.5 8.81802 16.5 9.87868 16.5 12C16.5 14.1213 16.5 15.182 15.841 15.841C15.182 16.5 14.1213 16.5 12 16.5H6C3.87868 16.5 2.81802 16.5 2.15901 15.841C1.5 15.182 1.5 14.1213 1.5 12Z"
                                                        fill="currentColor"
                                                    />
                                                    <path
                                                        d="M6 12.75C6.41421 12.75 6.75 12.4142 6.75 12C6.75 11.5858 6.41421 11.25 6 11.25C5.58579 11.25 5.25 11.5858 5.25 12C5.25 12.4142 5.58579 12.75 6 12.75Z"
                                                        fill="currentColor"
                                                    />
                                                    <path
                                                        d="M9 12.75C9.41421 12.75 9.75 12.4142 9.75 12C9.75 11.5858 9.41421 11.25 9 11.25C8.58579 11.25 8.25 11.5858 8.25 12C8.25 12.4142 8.58579 12.75 9 12.75Z"
                                                        fill="currentColor"
                                                    />
                                                    <path
                                                        d="M12.75 12C12.75 12.4142 12.4142 12.75 12 12.75C11.5858 12.75 11.25 12.4142 11.25 12C11.25 11.5858 11.5858 11.25 12 11.25C12.4142 11.25 12.75 11.5858 12.75 12Z"
                                                        fill="currentColor"
                                                    />
                                                    <path
                                                        d="M5.0625 6C5.0625 3.82538 6.82538 2.0625 9 2.0625C11.1746 2.0625 12.9375 3.82538 12.9375 6V7.50268C13.363 7.50665 13.7351 7.51651 14.0625 7.54096V6C14.0625 3.20406 11.7959 0.9375 9 0.9375C6.20406 0.9375 3.9375 3.20406 3.9375 6V7.54096C4.26488 7.51651 4.63698 7.50665 5.0625 7.50268V6Z"
                                                        fill="currentColor"
                                                    />
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Hidden role field - value is set by the card selection */}
                                <input type="hidden" id="role" value={formData.role} />

                                {showVendorFields && (
                                    <div className="gap-5 grid grid-cols-2">
                                        <div>
                                            <label htmlFor="company_name">Company Name</label>
                                            <div className="relative text-white-dark">
                                                <input
                                                    id="company_name"
                                                    type="text"
                                                    placeholder="Enter Company Name"
                                                    className="placeholder:text-white-dark form-input ps-10"
                                                    value={formData.company_name}
                                                    onChange={handleChange}
                                                    required={showVendorFields}
                                                />
                                                <span className="top-1/2 absolute -translate-y-1/2 start-4">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path opacity="0.5" d="M2 8.5H22" stroke="#888EA8" strokeWidth="1.5" strokeLinecap="round" />
                                                        <path d="M6 16.5H8" stroke="#888EA8" strokeWidth="1.5" strokeLinecap="round" />
                                                        <path d="M11 16.5H16" stroke="#888EA8" strokeWidth="1.5" strokeLinecap="round" />
                                                        <path d="M6.44 3.5H17.55C21.11 3.5 22 4.38 22 7.89V16.1C22 19.62 21.11 20.5 17.56 20.5H6.44C2.89 20.5 2 19.62 2 16.11V7.89C2 4.38 2.89 3.5 6.44 3.5Z" stroke="#888EA8" strokeWidth="1.5" strokeLinecap="round" />
                                                    </svg>
                                                </span>
                                            </div>
                                        </div>
                                        <div>
                                            <label htmlFor="company_domain">Company Domain</label>
                                            <div className="relative text-white-dark">
                                                <input
                                                    id="company_domain"
                                                    type="text"
                                                    placeholder="Enter Company Domain"
                                                    className="placeholder:text-white-dark form-input ps-10"
                                                    value={formData.company_domain}
                                                    onChange={handleChange}
                                                    required={showVendorFields}
                                                />
                                                <span className="top-1/2 absolute -translate-y-1/2 start-4">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path opacity="0.5" d="M2 8.5H22" stroke="#888EA8" strokeWidth="1.5" strokeLinecap="round" />
                                                        <path d="M6 16.5H8" stroke="#888EA8" strokeWidth="1.5" strokeLinecap="round" />
                                                        <path d="M11 16.5H16" stroke="#888EA8" strokeWidth="1.5" strokeLinecap="round" />
                                                        <path d="M6.44 3.5H17.55C21.11 3.5 22 4.38 22 7.89V16.1C22 19.62 21.11 20.5 17.56 20.5H6.44C2.89 20.5 2 19.62 2 16.11V7.89C2 4.38 2.89 3.5 6.44 3.5Z" stroke="#888EA8" strokeWidth="1.5" strokeLinecap="round" />
                                                    </svg>
                                                </span>
                                            </div>
                                            <small className="block mt-1 text-xs">This will be used for your subdomain: {formData.company_domain ? `${formData.company_domain}.${centralDomain}` : `yourdomain.${centralDomain}`}</small>
                                        </div>
                                    </div>
                                )}

                                <button type="submit" className="shadow-[0_10px_20px_-10px_rgba(67,97,238,0.44)] !mt-6 border-0 w-full uppercase btn btn-gradient">
                                    Sign Up
                                </button>
                            </form>


                            <div className="mt-8 max-md:mt-4 text-center dark:text-white">
                                Already have an account ?&nbsp;
                                <Link href="/auth/login" className="text-primary hover:text-black dark:hover:text-white underline uppercase transition">
                                    SIGN IN
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RegisterBoxed;


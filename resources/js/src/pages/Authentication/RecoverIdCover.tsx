import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { setPageTitle, toggleRTL } from '../../store/themeConfigSlice';
import Dropdown from '../../components/Dropdown';
import { IRootState } from '../../store';
import i18next from 'i18next';

const RecoverIdCover = () => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Recover Id Box'));
    });
    const navigate = useNavigate();
    const isRtl = useSelector((state: IRootState) => state.themeConfig.rtlClass) === 'rtl' ? true : false;
    const themeConfig = useSelector((state: IRootState) => state.themeConfig);
    const setLocale = (flag: string) => {
        setFlag(flag);
        if (flag.toLowerCase() === 'ae') {
            dispatch(toggleRTL('rtl'));
        } else {
            dispatch(toggleRTL('ltr'));
        }
    };
    const [flag, setFlag] = useState(themeConfig.locale);

    const submitForm = () => {
        navigate('/');
    };

    return (
        <div>
            <div className="absolute inset-0">
                <img src="/assets/images/auth/bg-gradient.png" alt="image" className="w-full h-full object-cover" />
            </div>
            <div className="relative flex justify-center items-center bg-[url(/assets/images/auth/map.png)] dark:bg-[#060818] bg-cover bg-no-repeat bg-center px-6 sm:px-16 py-10 min-h-screen">
                <img src="/assets/images/auth/coming-soon-object1.png" alt="image" className="top-1/2 left-0 absolute h-full max-h-[893px] -translate-y-1/2" />
                <img src="/assets/images/auth/coming-soon-object2.png" alt="image" className="top-0 left-24 md:left-[30%] absolute h-40" />
                <img src="/assets/images/auth/coming-soon-object3.png" alt="image" className="top-0 right-0 absolute h-[300px]" />
                <img src="/assets/images/auth/polygon-object.svg" alt="image" className="bottom-0 absolute end-[28%]" />
                <div className="relative flex lg:flex-row flex-col justify-between lg:gap-10 xl:gap-0 bg-white/60 dark:bg-black/50 backdrop-blur-lg rounded-md w-full max-w-[1502px] lg:min-h-[758px] overflow-hidden">
                    <div className="lg:inline-flex relative justify-center items-center hidden bg-[linear-gradient(225deg,rgba(239,18,98,1)_0%,rgba(67,97,238,1)_100%)] p-5 w-full lg:max-w-[835px] xl:-ms-28 ltr:xl:skew-x-[14deg] rtl:xl:skew-x-[-14deg]">
                        <div className="ltr:-right-10 ltr:xl:-right-20 rtl:-left-10 rtl:xl:-left-20 absolute inset-y-0 ltr:bg-gradient-to-r rtl:bg-gradient-to-l from-primary/10 via-transparent to-transparent w-8 xl:w-16"></div>
                        <div className="ltr:xl:-skew-x-[14deg] rtl:xl:skew-x-[14deg]">
                            <Link to="/" className="block w-48 lg:w-72 ms-10">
                                <img src="/assets/images/auth/logo-white.svg" alt="Logo" className="w-full" />
                            </Link>
                            <div className="lg:block hidden mt-24 w-full max-w-[430px]">
                                <img src="/assets/images/auth/reset-password.svg" alt="Cover Image" className="w-full" />
                            </div>
                        </div>
                    </div>
                    <div className="relative flex flex-col justify-center items-center gap-6 px-4 sm:px-6 pt-6 pb-16 w-full lg:max-w-[667px]">
                        <div className="lg:top-6 lg:absolute flex items-center gap-2 w-full max-w-[440px] lg:max-w-full lg:end-6">
                            <Link to="/" className="block lg:hidden w-8">
                                <img src="/assets/images/logo.svg" alt="Logo" className="mx-auto w-10" />
                            </Link>
                            <div className="w-max dropdown ms-auto">
                                <Dropdown
                                    offset={[0, 8]}
                                    placement={`${isRtl ? 'bottom-start' : 'bottom-end'}`}
                                    btnClassName="flex items-center gap-2.5 rounded-lg border border-white-dark/30 bg-white px-2 py-1.5 text-white-dark hover:border-primary hover:text-primary dark:bg-black"
                                    button={
                                        <>
                                            <div>
                                                <img src={`/assets/images/flags/${flag.toUpperCase()}.svg`} alt="image" className="rounded-full w-5 h-5 object-cover" />
                                            </div>
                                            <div className="font-bold text-base uppercase">{flag}</div>
                                            <span className="shrink-0">
                                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M6.99989 9.79988C6.59156 9.79988 6.18322 9.64238 5.87406 9.33321L2.07072 5.52988C1.90156 5.36071 1.90156 5.08071 2.07072 4.91154C2.23989 4.74238 2.51989 4.74238 2.68906 4.91154L6.49239 8.71488C6.77239 8.99488 7.22739 8.99488 7.50739 8.71488L11.3107 4.91154C11.4799 4.74238 11.7599 4.74238 11.9291 4.91154C12.0982 5.08071 12.0982 5.36071 11.9291 5.52988L8.12572 9.33321C7.81656 9.64238 7.40822 9.79988 6.99989 9.79988Z"
                                                        fill="currentColor"
                                                    />
                                                </svg>
                                            </span>
                                        </>
                                    }
                                >
                                    <ul className="gap-2 grid grid-cols-2 !px-2 w-[280px] font-semibold text-dark dark:text-white-dark dark:text-white-light/90">
                                        {themeConfig.languageList.map((item: any) => {
                                            return (
                                                <li key={item.code}>
                                                    <button
                                                        type="button"
                                                        className={`flex w-full hover:text-primary rounded-lg ${flag === item.code ? 'bg-primary/10 text-primary' : ''}`}
                                                        onClick={() => {
                                                            i18next.changeLanguage(item.code);
                                                            // setFlag(item.code);
                                                            setLocale(item.code);
                                                        }}
                                                    >
                                                        <img src={`/assets/images/flags/${item.code.toUpperCase()}.svg`} alt="flag" className="rounded-full w-5 h-5 object-cover" />
                                                        <span className="rtl:mr-3 ltr:ml-3">{item.name}</span>
                                                    </button>
                                                </li>
                                            );
                                        })}
                                    </ul>
                                </Dropdown>
                            </div>
                        </div>
                        <div className="lg:mt-16 w-full max-w-[440px]">
                            <div className="mb-7">
                                <h1 className="mb-3 font-bold text-2xl dark:text-white !leading-snug">Password Reset</h1>
                                <p>Enter your email to recover your ID</p>
                            </div>
                            <form className="space-y-5" onSubmit={submitForm}>
                                <div>
                                    <label htmlFor="Email">Email</label>
                                    <div className="relative text-white-dark">
                                        <input id="Email" type="email" placeholder="Enter Email" className="pl-10 placeholder:text-white-dark form-input" />
                                        <span className="top-1/2 left-4 absolute -translate-y-1/2">
                                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                <path
                                                    opacity="0.5"
                                                    d="M10.65 2.25H7.35C4.23873 2.25 2.6831 2.25 1.71655 3.23851C0.75 4.22703 0.75 5.81802 0.75 9C0.75 12.182 0.75 13.773 1.71655 14.7615C2.6831 15.75 4.23873 15.75 7.35 15.75H10.65C13.7613 15.75 15.3169 15.75 16.2835 14.7615C17.25 13.773 17.25 12.182 17.25 9C17.25 5.81802 17.25 4.22703 16.2835 3.23851C15.3169 2.25 13.7613 2.25 10.65 2.25Z"
                                                    fill="currentColor"
                                                />
                                                <path
                                                    d="M14.3465 6.02574C14.609 5.80698 14.6445 5.41681 14.4257 5.15429C14.207 4.89177 13.8168 4.8563 13.5543 5.07507L11.7732 6.55931C11.0035 7.20072 10.4691 7.6446 10.018 7.93476C9.58125 8.21564 9.28509 8.30993 9.00041 8.30993C8.71572 8.30993 8.41956 8.21564 7.98284 7.93476C7.53168 7.6446 6.9973 7.20072 6.22761 6.55931L4.44652 5.07507C4.184 4.8563 3.79384 4.89177 3.57507 5.15429C3.3563 5.41681 3.39177 5.80698 3.65429 6.02574L5.4664 7.53583C6.19764 8.14522 6.79033 8.63914 7.31343 8.97558C7.85834 9.32604 8.38902 9.54743 9.00041 9.54743C9.6118 9.54743 10.1425 9.32604 10.6874 8.97558C11.2105 8.63914 11.8032 8.14522 12.5344 7.53582L14.3465 6.02574Z"
                                                    fill="currentColor"
                                                />
                                            </svg>
                                        </span>
                                    </div>
                                </div>
                                <button type="submit" className="shadow-[0_10px_20px_-10px_rgba(67,97,238,0.44)] !mt-6 border-0 w-full uppercase btn btn-gradient">
                                    RECOVER
                                </button>
                            </form>
                        </div>
                        <p className="bottom-6 absolute w-full text-center dark:text-white">© {new Date().getFullYear()}.BooknBlush All Rights Reserved.</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RecoverIdCover;

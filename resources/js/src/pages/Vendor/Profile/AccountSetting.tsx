import { Link, useForm, router } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import { setPageTitle } from '@/store/themeConfigSlice';
import { useDispatch } from 'react-redux';
import Swal from 'sweetalert2';
import axios from 'axios';

interface UserProfile {
    name: string;
    email: string;
    phone: string | null;
    gender: 'male' | 'female' | 'other' | null;
    address: string | null;
    logo: string | null;
}

interface Props {
    user: UserProfile;
}

const AccountSetting = ({ user }: Props) => {
    const dispatch = useDispatch();
    const [tabs, setTabs] = useState('home');
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    const profileForm = useForm({
        name: user.name,
        email: user.email,
        phone: user.phone || '',
        gender: user.gender || '',
        address: user.address || '',
        logo: null as File | null
    });

    const passwordForm = useForm({
        current_password: '',
        password: '',
        password_confirmation: ''
    });

    useEffect(() => {
        dispatch(setPageTitle('Account Settings'));
    }, []);

    const showMessage = (msg = '', type = 'success') => {
        const toast: any = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            customClass: { container: 'toast' },
        });
        toast.fire({
            icon: type,
            title: msg,
            padding: '10px 20px',
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setSelectedFile(file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreviewUrl(reader.result as string);
            };
            reader.readAsDataURL(file);
            profileForm.setData('logo', file);
        }
    };

    const handleProfileUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        
        const formData = new FormData();
        formData.append('name', profileForm.data.name);
        formData.append('email', profileForm.data.email);
        
        if (profileForm.data.phone) {
            formData.append('phone', profileForm.data.phone);
        }
        if (profileForm.data.gender) {
            formData.append('gender', profileForm.data.gender);
        }
        if (profileForm.data.address) {
            formData.append('address', profileForm.data.address);
        }
        
        if (selectedFile) {
            formData.append('logo', selectedFile);
        }

        axios.post(route('vendor.profile.update'), formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            showMessage('Profile updated successfully');
            setSelectedFile(null);
            router.reload({ only: ['auth', 'site_branches','user'] });
            //setPreviewUrl(null);
        })
        .catch(error => {
            if (error.response?.status === 422) {
                const errors = error.response.data.errors;
                const errorMessage = Object.values(errors).flat().join('\n');
                showMessage(errorMessage, 'error');
            } else {
                showMessage('Failed to update profile', 'error');
            }
        });
    };

    const handlePasswordUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        
        axios.post(route('vendor.profile.password'), passwordForm.data, {
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            showMessage('Password updated successfully');
            passwordForm.reset();
        })
        .catch(error => {
            if (error.response?.status === 422) {
                const errors = error.response.data.errors;
                const errorMessage = Object.values(errors).flat().join('\n');
                showMessage(errorMessage, 'error');
            } else {
                showMessage('Failed to update password', 'error');
            }
        });
    };

    const toggleTabs = (name: string) => {
        setTabs(name);
    };

    const getProfileImageUrl = (logoPath: string | null) => {
        if (!logoPath) return "/assets/images/profile-34.jpeg";
        
        // Get the current tenant's domain
        const tenantDomain = window.location.hostname;
        // Construct the full URL for the tenant's storage
        return `https://${tenantDomain}/${logoPath}`;
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Account Settings</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="flex items-center justify-between mb-5">
                    <h5 className="font-semibold text-lg dark:text-white-light">Settings</h5>
                </div>
                <div>
                    <ul className="sm:flex font-semibold border-b border-[#ebedf2] dark:border-[#191e3a] mb-5 whitespace-nowrap overflow-y-auto">
                        <li className="inline-block">
                            <button
                                onClick={() => toggleTabs('home')}
                                className={`flex gap-2 p-4 border-b border-transparent hover:border-primary hover:text-primary ${tabs === 'home' ? '!border-primary text-primary' : ''}`}
                            >
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-5 h-5">
                                    <path
                                        opacity="0.5"
                                        d="M2 12.2039C2 9.91549 2 8.77128 2.5192 7.82274C3.0384 6.87421 3.98695 6.28551 5.88403 5.10813L7.88403 3.86687C9.88939 2.62229 10.8921 2 12 2C13.1079 2 14.1106 2.62229 16.116 3.86687L18.116 5.10812C20.0131 6.28551 20.9616 6.87421 21.4808 7.82274C22 8.77128 22 9.91549 22 12.2039V13.725C22 17.6258 22 19.5763 20.8284 20.7881C19.6569 22 17.7712 22 14 22H10C6.22876 22 4.34315 22 3.17157 20.7881C2 19.5763 2 17.6258 2 13.725V12.2039Z"
                                        stroke="currentColor"
                                        strokeWidth="1.5"
                                    />
                                    <path d="M12 15L12 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                </svg>
                                Profile
                            </button>
                        </li>
                        <li className="inline-block">
                            <button
                                onClick={() => toggleTabs('password')}
                                className={`flex gap-2 p-4 border-b border-transparent hover:border-primary hover:text-primary ${tabs === 'password' ? '!border-primary text-primary' : ''}`}
                            >
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-5 h-5">
                                    <circle opacity="0.5" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" />
                                    <path d="M12 6V18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                    <path
                                        d="M15 9.5C15 8.11929 13.6569 7 12 7C10.3431 7 9 8.11929 9 9.5C9 10.8807 10.3431 12 12 12C13.6569 12 15 13.1193 15 14.5C15 15.8807 13.6569 17 12 17C10.3431 17 9 15.8807 9 14.5"
                                        stroke="currentColor"
                                        strokeWidth="1.5"
                                    />
                                </svg>
                                Password
                            </button>
                        </li>
                    </ul>
                </div>
                {tabs === 'home' ? (
                    <div>
                        <form onSubmit={handleProfileUpdate} className="border border-[#ebedf2] dark:border-[#191e3a] rounded-md p-4 mb-5 bg-white dark:bg-black">
                            <h6 className="text-lg font-bold mb-5">General Information</h6>
                            <div className="flex flex-col sm:flex-row">
                                <div className="ltr:sm:mr-4 rtl:sm:ml-4 w-full sm:w-2/12 mb-5">
                                    <div className="relative">
                                        <img 
                                            src={previewUrl || getProfileImageUrl(user.logo)} 
                                            alt="profile" 
                                            className="w-20 h-20 md:w-32 md:h-32 rounded-full object-cover mx-auto"
                                        />
                                        <input
                                            type="file"
                                            id="logo"
                                            className="hidden"
                                            onChange={handleFileChange}
                                            accept="image/*"
                                        />
                                        <label
                                            htmlFor="logo"
                                            className="absolute bottom-0 right-0 bg-primary text-white p-2 rounded-full cursor-pointer"
                                        >
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 4V20M4 12H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                                            </svg>
                                        </label>
                                    </div>
                                </div>
                                <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 gap-5">
                                    <div>
                                        <label htmlFor="name">Full Name</label>
                                        <input
                                            id="name"
                                            type="text"
                                            value={profileForm.data.name}
                                            onChange={(e) => profileForm.setData('name', e.target.value)}
                                            className="form-input"
                                        />
                                    </div>
                                    <div>
                                        <label htmlFor="gender">Gender</label>
                                        <select
                                            id="gender"
                                            value={profileForm.data.gender}
                                            onChange={(e) => profileForm.setData('gender', e.target.value)}
                                            className="form-select"
                                        >
                                            <option value="">Select Gender</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label htmlFor="address">Address</label>
                                        <input
                                            id="address"
                                            type="text"
                                            value={profileForm.data.address}
                                            onChange={(e) => profileForm.setData('address', e.target.value)}
                                            className="form-input"
                                        />
                                    </div>
                                    <div>
                                        <label htmlFor="phone">Phone</label>
                                        <input
                                            id="phone"
                                            type="text"
                                            value={profileForm.data.phone}
                                            onChange={(e) => profileForm.setData('phone', e.target.value)}
                                            className="form-input"
                                        />
                                    </div>
                                    <div>
                                        <label htmlFor="email">Email</label>
                                        <input
                                            id="email"
                                            type="email"
                                            value={profileForm.data.email}
                                            onChange={(e) => profileForm.setData('email', e.target.value)}
                                            className="form-input"
                                        />
                                    </div>
                                    <div className="sm:col-span-2 mt-3">
                                        <button type="submit" className="btn btn-primary" disabled={profileForm.processing}>
                                            {profileForm.processing ? 'Saving...' : 'Save'}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                ) : (
                    <div>
                        <form onSubmit={handlePasswordUpdate} className="border border-[#ebedf2] dark:border-[#191e3a] rounded-md p-4 bg-white dark:bg-black">
                            <h6 className="text-lg font-bold mb-5">Change Password</h6>
                            <div className="grid grid-cols-1 gap-5">
                                <div>
                                    <label htmlFor="current_password">Current Password</label>
                                    <input
                                        id="current_password"
                                        type="password"
                                        value={passwordForm.data.current_password}
                                        onChange={(e) => passwordForm.setData('current_password', e.target.value)}
                                        className="form-input"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="password">New Password</label>
                                    <input
                                        id="password"
                                        type="password"
                                        value={passwordForm.data.password}
                                        onChange={(e) => passwordForm.setData('password', e.target.value)}
                                        className="form-input"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="password_confirmation">Confirm New Password</label>
                                    <input
                                        id="password_confirmation"
                                        type="password"
                                        value={passwordForm.data.password_confirmation}
                                        onChange={(e) => passwordForm.setData('password_confirmation', e.target.value)}
                                        className="form-input"
                                    />
                                </div>
                                <div>
                                    <button type="submit" className="btn btn-primary" disabled={passwordForm.processing}>
                                        {passwordForm.processing ? 'Updating...' : 'Update Password'}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AccountSetting;

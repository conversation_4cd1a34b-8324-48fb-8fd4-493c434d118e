import React, { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
}

interface Props {
    services: Service[];
}

const Create = ({ services }: Props) => {
    const dispatch = useDispatch();
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        price: '',
        validity_days: '',
        status: 'active',
        services: [] as { service_id: number; allowed_count: number }[],
    });

    useEffect(() => {
        dispatch(setPageTitle('Create Plan'));
    });

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleServiceChange = (serviceId: number, allowedCount: number) => {
        setFormData((prev) => {
            const existingServiceIndex = prev.services.findIndex((s) => s.service_id === serviceId);
            const updatedServices = [...prev.services];

            if (existingServiceIndex !== -1) {
                if (allowedCount === 0) {
                    updatedServices.splice(existingServiceIndex, 1);
                } else {
                    updatedServices[existingServiceIndex].allowed_count = allowedCount;
                }
            } else if (allowedCount > 0) {
                updatedServices.push({ service_id: serviceId, allowed_count: allowedCount });
            }

            return {
                ...prev,
                services: updatedServices,
            };
        });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        router.post('/vendor/plans', formData);
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/plans" className="text-primary hover:underline">
                        Plans
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Create</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <form onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 gap-4 mb-5">
                            <div>
                                <label htmlFor="name">Name</label>
                                <input
                                    id="name"
                                    type="text"
                                    name="name"
                                    className="form-input"
                                    value={formData.name}
                                    onChange={handleInputChange}
                                    required
                                />
                            </div>
                            <div>
                                <label htmlFor="description">Description</label>
                                <textarea
                                    id="description"
                                    name="description"
                                    className="form-textarea"
                                    value={formData.description}
                                    onChange={handleInputChange}
                                    rows={3}
                                />
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label htmlFor="price">Price (₹)</label>
                                    <input
                                        id="price"
                                        type="number"
                                        name="price"
                                        className="form-input"
                                        value={formData.price}
                                        onChange={handleInputChange}
                                        min="0"
                                        step="0.01"
                                        required
                                    />
                                </div>
                                <div>
                                    <label htmlFor="validity_days">Validity (Days)</label>
                                    <input
                                        id="validity_days"
                                        type="number"
                                        name="validity_days"
                                        className="form-input"
                                        value={formData.validity_days}
                                        onChange={handleInputChange}
                                        min="1"
                                        required
                                    />
                                </div>
                            </div>
                            <div>
                                <label htmlFor="status">Status</label>
                                <select
                                    id="status"
                                    name="status"
                                    className="form-select"
                                    value={formData.status}
                                    onChange={handleInputChange}
                                >
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>

                        <div className="mb-5">
                            <h5 className="font-semibold text-lg mb-4">Services</h5>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {services.map((service) => (
                                    <div key={service.id} className="border rounded-md p-4">
                                        <div className="flex justify-between items-center mb-2">
                                            <span className="font-semibold">{service.name}</span>
                                            <span className="text-primary">₹{service.price}</span>
                                        </div>
                                        <div className="text-sm text-gray-500 mb-2">
                                            Duration: {service.duration_minutes} minutes
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <label htmlFor={`service-${service.id}`} className="text-sm">
                                                Allowed Count:
                                            </label>
                                            <input
                                                id={`service-${service.id}`}
                                                type="number"
                                                className="form-input w-20"
                                                min="0"
                                                value={
                                                    formData.services.find((s) => s.service_id === service.id)
                                                        ?.allowed_count || 0
                                                }
                                                onChange={(e) =>
                                                    handleServiceChange(service.id, parseInt(e.target.value))
                                                }
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="flex justify-end gap-4">
                            <Link href="/vendor/plans" className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary">
                                Create Plan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Create; 
import { useEffect } from 'react';
import { Link, useForm } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';

interface Props {
    branch: {
        id: number;
        name: string;
        address: string;
        phone: string;
        email: string | null;
        is_active: boolean;
    };
}

const Edit = ({ branch }: Props) => {
    const dispatch = useDispatch();
    const { data, setData, put, processing, errors } = useForm({
        name: branch.name,
        address: branch.address,
        phone: branch.phone,
        email: branch.email || '',
        is_active: branch.is_active,
    });

    useEffect(() => {
        dispatch(setPageTitle('Edit Branch'));
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/vendor/branches/${branch.id}`, {
            onSuccess: () => {
                // Form will be reset automatically on success
            },
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/branches" className="text-primary hover:underline">
                        Branches
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Edit</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Edit Branch</h5>
                    </div>

                    <form className="space-y-5" onSubmit={handleSubmit}>
                        <div>
                            <label htmlFor="name">Name <span className="text-danger">*</span></label>
                            <input
                                id="name"
                                type="text"
                                className="form-input"
                                value={data.name}
                                onChange={(e) => setData('name', e.target.value)}
                                required
                            />
                            {errors.name && <div className="text-danger mt-1">{errors.name}</div>}
                        </div>

                        <div>
                            <label htmlFor="address">Address <span className="text-danger">*</span></label>
                            <textarea
                                id="address"
                                className="form-textarea"
                                value={data.address}
                                onChange={(e) => setData('address', e.target.value)}
                                required
                            />
                            {errors.address && <div className="text-danger mt-1">{errors.address}</div>}
                        </div>

                        <div>
                            <label htmlFor="phone">Phone <span className="text-danger">*</span></label>
                            <input
                                id="phone"
                                type="text"
                                className="form-input"
                                value={data.phone}
                                onChange={(e) => setData('phone', e.target.value)}
                                required
                            />
                            {errors.phone && <div className="text-danger mt-1">{errors.phone}</div>}
                        </div>

                        <div>
                            <label htmlFor="email">Email</label>
                            <input
                                id="email"
                                type="email"
                                className="form-input"
                                value={data.email}
                                onChange={(e) => setData('email', e.target.value)}
                            />
                            {errors.email && <div className="text-danger mt-1">{errors.email}</div>}
                        </div>

                        <div>
                            <label className="inline-flex">
                                <input
                                    type="checkbox"
                                    className="form-checkbox"
                                    checked={data.is_active}
                                    onChange={(e) => setData('is_active', e.target.checked)}
                                />
                                <span>Active</span>
                            </label>
                        </div>

                        <div className="flex items-center justify-end gap-4">
                            <Link href="/vendor/branches" className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary" disabled={processing}>
                                {processing ? 'Saving...' : 'Save Changes'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Edit; 
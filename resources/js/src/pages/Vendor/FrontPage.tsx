import React, { useState, Fragment } from 'react';
import { Link, useForm, router } from '@inertiajs/react';
import { Dialog, Transition } from '@headlessui/react';
import Flatpickr from 'react-flatpickr';
import 'flatpickr/dist/flatpickr.css';
import {
    FaCalendarAlt, FaClock, FaUser, FaEnvelope, FaPhone, FaDollarSign,
    FaMinusCircle, FaPlusCircle, FaShoppingCart, FaCheck, FaStar,
    FaMapMarkerAlt, FaInstagram, FaFacebook, FaTwitter, FaBuilding,
    FaTag, FaHeart, FaGift, FaSparkles, FaCrown, FaFire, FaGem,
    FaWhatsapp, FaLinkedin, FaYoutube, FaTiktok, FaArrowRight,
    FaPlay, FaQuoteLeft, FaAward, FaUsers, FaThumbsUp
} from 'react-icons/fa';

interface Service {
    id: number;
    name: string;
    description: string;
    duration_minutes: number;
    price: number;
    image: string;
}

interface Plan {
    id: number;
    name: string;
    description: string;
    price: number;
    validity_days: number;
    status: string;
    services: Array<{
        id: number;
        name: string;
        allowed_count: number;
    }>;
}

interface Branch {
    id: number;
    name: string;
    address: string;
}

interface Tenant {
    id: string;
    name: string;
    address: string;
    phone: string;
    email: string;
    logo: string;
    branches: Branch[];
}

interface PageProps {
    tenant: Tenant;
    services: Service[];
    plans: Plan[];
    selectedBranchId: number;
    layout: string;
}

interface FormData {
    name: string;
    email: string;
    phone: string;
    appointment_date: string;
    appointment_time: string;
    services: Service[];
    password: string;
    notes: string;
    branch_id: number;
}

interface FormErrors {
    name?: string;
    email?: string;
    phone?: string;
    appointment_date?: string;
    appointment_time?: string;
    password?: string;
    notes?: string;
    branch_id?: string;
    [key: string]: string | undefined;
}

const FrontPage = ({ tenant, services, plans, selectedBranchId, layout = 'blank' }: PageProps) => {
    const [selectedServices, setSelectedServices] = useState<Service[]>([]);
    const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
    const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
    const [addedServices, setAddedServices] = useState<Record<number, boolean>>({});
    const [showCart, setShowCart] = useState(false);
    const [selectedBranch, setSelectedBranch] = useState<number>(selectedBranchId);

    const { data, setData, post, processing, errors, reset } = useForm<FormData>({
        name: '',
        email: '',
        phone: '',
        appointment_date: '',
        appointment_time: '',
        services: [],
        password: '',
        notes: '',
        branch_id: selectedBranch,
    });

    // Handle branch selection
    const handleBranchChange = (branchId: number) => {
        setSelectedBranch(branchId);
        setSelectedServices([]); // Clear selected services when branch changes
        setShowCart(false); // Hide cart when branch changes

        // Update URL and reload data
        router.get(
            window.location.pathname,
            { branch_id: branchId },
            { preserveState: true, preserveScroll: true }
        );
    };

    const addToCart = (service: Service) => {
        if (!selectedServices.find(s => s.id === service.id)) {
            setSelectedServices([...selectedServices, service]);
            setAddedServices({ ...addedServices, [service.id]: true });
            setShowCart(true);

            setTimeout(() => {
                setAddedServices(prev => ({ ...prev, [service.id]: false }));
            }, 2000);
        }
    };

    const removeFromCart = (serviceId: number) => {
        setSelectedServices(selectedServices.filter(s => s.id !== serviceId));
    };

    const calculateTotal = () => {
        return selectedServices.reduce((sum, service) => sum + service.price, 0);
    };

    const calculateTotalDuration = () => {
        return selectedServices.reduce((sum, service) => sum + service.duration_minutes, 0);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const formData = {
            ...data,
            services: selectedServices,
            branch_id: selectedBranch,
        };
        post('/book', formData);
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-100 font-sans overflow-x-hidden">
            {/* Floating Elements Background */}
            <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
                <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-pink-300/20 to-purple-300/20 rounded-full blur-xl animate-pulse"></div>
                <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-blue-300/20 to-indigo-300/20 rounded-full blur-xl animate-pulse delay-1000"></div>
                <div className="absolute bottom-40 left-20 w-40 h-40 bg-gradient-to-r from-purple-300/20 to-pink-300/20 rounded-full blur-xl animate-pulse delay-2000"></div>
                <div className="absolute bottom-20 right-10 w-28 h-28 bg-gradient-to-r from-indigo-300/20 to-blue-300/20 rounded-full blur-xl animate-pulse delay-500"></div>
            </div>

            {/* Branch Selection - Modern Mobile-First Design */}
            {tenant.branches.length > 1 && (
                <div className="relative z-30 bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-lg sticky top-0">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                            <h2 className="text-lg font-bold text-gray-900 flex items-center">
                                <div className="bg-gradient-to-r from-primary to-purple-600 p-2 rounded-xl mr-3">
                                    <FaBuilding className="text-white text-sm" />
                                </div>
                                Select Your Branch
                            </h2>
                            <select
                                value={selectedBranch}
                                onChange={(e) => handleBranchChange(Number(e.target.value))}
                                className="w-full sm:w-64 rounded-2xl border-0 bg-white/90 backdrop-blur-sm shadow-lg focus:ring-2 focus:ring-primary/50 focus:border-transparent py-3 px-4 text-sm font-medium"
                            >
                                {tenant.branches.map((branch) => (
                                    <option key={branch.id} value={branch.id}>
                                        {branch.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </div>
            )}

            {/* Modern Mobile Cart Popup */}
            <Transition
                show={showCart}
                enter="transition ease-out duration-500"
                enterFrom="translate-x-full opacity-0"
                enterTo="translate-x-0 opacity-100"
                leave="transition ease-in duration-300"
                leaveFrom="translate-x-0 opacity-100"
                leaveTo="translate-x-full opacity-0"
            >
                <div className="fixed right-2 top-20 z-50 bg-white/95 backdrop-blur-xl shadow-2xl rounded-3xl p-4 sm:p-6 w-[calc(100vw-1rem)] sm:w-96 max-h-[80vh] overflow-hidden border border-white/20">
                    <div className="flex items-center justify-between mb-6">
                        <h3 className="text-xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent flex items-center">
                            <div className="bg-gradient-to-r from-primary to-purple-600 p-2 rounded-xl mr-3">
                                <FaShoppingCart className="text-white text-sm" />
                            </div>
                            Your Cart
                        </h3>
                        <button
                            onClick={() => setShowCart(false)}
                            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
                        >
                            <FaMinusCircle className="h-5 w-5 text-gray-600" />
                        </button>
                    </div>
                    <div className="space-y-3 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent">
                        {selectedServices.map((service) => (
                            <div key={service.id} className="bg-gradient-to-r from-primary/5 via-purple/5 to-pink/5 p-4 rounded-2xl border border-white/30 hover:shadow-lg transition-all duration-300">
                                <div className="flex items-center justify-between">
                                    <div className="flex-1">
                                        <h4 className="font-semibold text-gray-800 text-sm sm:text-base">{service.name}</h4>
                                        <p className="text-xs text-gray-500 mt-1">
                                            <FaClock className="inline mr-1" />
                                            {service.duration_minutes} mins
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <span className="text-primary font-bold text-lg">₹{service.price}</span>
                                        <button
                                            onClick={() => removeFromCart(service.id)}
                                            className="p-2 rounded-full bg-red-50 hover:bg-red-100 text-red-500 hover:text-red-600 transition-colors duration-200"
                                        >
                                            <FaMinusCircle className="h-4 w-4" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                    {selectedServices.length > 0 && (
                        <div className="mt-6 pt-4 border-t border-gray-200/50">
                            <div className="bg-gradient-to-r from-primary/10 to-purple/10 p-4 rounded-2xl">
                                <div className="flex justify-between items-center mb-2">
                                    <span className="font-medium text-gray-700 text-sm">Duration:</span>
                                    <span className="text-gray-800 font-semibold">{calculateTotalDuration()} mins</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="font-medium text-gray-700">Total:</span>
                                    <span className="text-primary font-bold text-2xl">₹{calculateTotal()}</span>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </Transition>

            {/* Modern Floating Book Button */}
            {selectedServices.length > 0 && (
                <div className="fixed bottom-4 left-4 right-4 z-50">
                    <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-4 sm:p-6">
                        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                            <div className="text-center sm:text-left">
                                <div className="flex items-center justify-center sm:justify-start gap-2 mb-1">
                                    <FaShoppingCart className="text-primary" />
                                    <span className="text-gray-600 font-medium">{selectedServices.length} services selected</span>
                                </div>
                                <div className="flex items-center justify-center sm:justify-start gap-2">
                                    <FaGem className="text-primary text-sm" />
                                    <span className="text-primary font-bold text-xl">₹{calculateTotal()}</span>
                                    <span className="text-gray-500 text-sm">• {calculateTotalDuration()} mins</span>
                                </div>
                            </div>
                            <button
                                onClick={() => setIsBookingModalOpen(true)}
                                className="w-full sm:w-auto inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-white bg-gradient-to-r from-primary via-purple-600 to-pink-600 rounded-2xl hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-primary/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1"
                            >
                                <FaCalendarAlt className="mr-3 h-5 w-5" />
                                Book Now
                                <FaArrowRight className="ml-3 h-4 w-4" />
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Modern Hero Section */}
            <div className="relative overflow-hidden">
                {/* Hero Background with Glassmorphism */}
                <div className="relative bg-gradient-to-br from-white/90 via-white/70 to-white/50 backdrop-blur-xl">
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-purple-500/5 to-pink-500/5" />

                    {/* Main Hero Content */}
                    <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20 lg:py-32">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">

                            {/* Left Column - Salon Info */}
                            <div className="text-center lg:text-left space-y-8">
                                {/* Logo and Title Section */}
                                <div className="space-y-6">
                                    <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-6">
                                        {tenant.logo && (
                                            <div className="relative">
                                                <div className="absolute inset-0 bg-gradient-to-r from-primary to-purple-600 rounded-3xl blur-lg opacity-30"></div>
                                                <img
                                                    src={tenant.logo}
                                                    alt={`${tenant.name} logo`}
                                                    className="relative h-20 w-20 sm:h-24 sm:w-24 rounded-3xl object-cover border-4 border-white shadow-2xl transform hover:scale-110 transition-all duration-500"
                                                />
                                            </div>
                                        )}
                                        <div className="space-y-3">
                                            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight">
                                                <span className="block bg-gradient-to-r from-primary via-purple-600 to-pink-600 bg-clip-text text-transparent">
                                                    {tenant.name}
                                                </span>
                                            </h1>

                                            {/* Rating and Badge */}
                                            <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4">
                                                <div className="flex items-center gap-2 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 px-4 py-2 rounded-full border border-yellow-400/30">
                                                    <div className="flex">
                                                        {[1, 2, 3, 4, 5].map((star) => (
                                                            <FaStar key={star} className="h-4 w-4 text-yellow-500" />
                                                        ))}
                                                    </div>
                                                    <span className="text-gray-700 font-semibold text-sm">4.9/5</span>
                                                </div>
                                                <div className="flex items-center gap-2 bg-gradient-to-r from-green-400/20 to-emerald-400/20 px-4 py-2 rounded-full border border-green-400/30">
                                                    <FaAward className="h-4 w-4 text-green-600" />
                                                    <span className="text-gray-700 font-semibold text-sm">Premium Salon</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Tagline */}
                                    <div className="max-w-2xl mx-auto lg:mx-0">
                                        <p className="text-xl sm:text-2xl text-gray-600 font-medium leading-relaxed">
                                            ✨ Transform your beauty journey with our premium services
                                        </p>
                                    </div>
                                </div>
                                {/* Contact Information */}
                                <div className="space-y-4">
                                    <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4 text-gray-700">
                                        <div className="flex items-center gap-3 bg-white/60 backdrop-blur-sm px-4 py-3 rounded-2xl border border-white/30 hover:bg-white/80 transition-all duration-300">
                                            <div className="bg-gradient-to-r from-primary to-purple-600 p-2 rounded-xl">
                                                <FaMapMarkerAlt className="h-4 w-4 text-white" />
                                            </div>
                                            <span className="font-medium text-sm sm:text-base">{tenant.address}</span>
                                        </div>
                                    </div>

                                    <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4">
                                        <div className="flex items-center gap-3 bg-white/60 backdrop-blur-sm px-4 py-3 rounded-2xl border border-white/30 hover:bg-white/80 transition-all duration-300">
                                            <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-2 rounded-xl">
                                                <FaPhone className="h-4 w-4 text-white" />
                                            </div>
                                            <span className="font-medium text-sm sm:text-base">{tenant.phone}</span>
                                        </div>

                                        <div className="flex items-center gap-3 bg-white/60 backdrop-blur-sm px-4 py-3 rounded-2xl border border-white/30 hover:bg-white/80 transition-all duration-300">
                                            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-2 rounded-xl">
                                                <FaEnvelope className="h-4 w-4 text-white" />
                                            </div>
                                            <span className="font-medium text-sm sm:text-base">{tenant.email}</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Social Media Links */}
                                <div className="flex items-center justify-center lg:justify-start gap-3">
                                    <span className="text-gray-600 font-medium mr-2">Follow us:</span>
                                    <a href="#" className="bg-gradient-to-r from-pink-500 to-rose-500 p-3 rounded-2xl text-white hover:shadow-lg transform hover:scale-110 transition-all duration-300">
                                        <FaInstagram className="h-5 w-5" />
                                    </a>
                                    <a href="#" className="bg-gradient-to-r from-blue-600 to-blue-700 p-3 rounded-2xl text-white hover:shadow-lg transform hover:scale-110 transition-all duration-300">
                                        <FaFacebook className="h-5 w-5" />
                                    </a>
                                    <a href="#" className="bg-gradient-to-r from-green-500 to-green-600 p-3 rounded-2xl text-white hover:shadow-lg transform hover:scale-110 transition-all duration-300">
                                        <FaWhatsapp className="h-5 w-5" />
                                    </a>
                                    <a href="#" className="bg-gradient-to-r from-red-500 to-red-600 p-3 rounded-2xl text-white hover:shadow-lg transform hover:scale-110 transition-all duration-300">
                                        <FaYoutube className="h-5 w-5" />
                                    </a>
                                </div>
                            </div>

                            {/* Right Column - Booking Card */}
                            <div className="relative">
                                {/* Floating decoration */}
                                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-pink-400/20 to-purple-400/20 rounded-full blur-xl"></div>
                                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-xl"></div>

                                <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/30 hover:shadow-3xl transition-all duration-500">
                                    {/* Header */}
                                    <div className="text-center mb-8">
                                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary to-purple-600 rounded-2xl mb-4">
                                            <FaCalendarAlt className="h-8 w-8 text-white" />
                                        </div>
                                        <h2 className="text-3xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent mb-3">
                                            Book Your Appointment
                                        </h2>
                                        <p className="text-gray-600 text-lg leading-relaxed">
                                            ✨ Experience premium beauty services with our expert professionals
                                        </p>
                                    </div>

                                    {/* Stats */}
                                    <div className="grid grid-cols-3 gap-4 mb-8">
                                        <div className="text-center p-4 bg-gradient-to-r from-primary/10 to-purple/10 rounded-2xl">
                                            <FaUsers className="h-6 w-6 text-primary mx-auto mb-2" />
                                            <div className="text-2xl font-bold text-gray-800">500+</div>
                                            <div className="text-xs text-gray-600">Happy Clients</div>
                                        </div>
                                        <div className="text-center p-4 bg-gradient-to-r from-green-400/10 to-emerald/10 rounded-2xl">
                                            <FaAward className="h-6 w-6 text-green-600 mx-auto mb-2" />
                                            <div className="text-2xl font-bold text-gray-800">5+</div>
                                            <div className="text-xs text-gray-600">Years Experience</div>
                                        </div>
                                        <div className="text-center p-4 bg-gradient-to-r from-yellow-400/10 to-orange/10 rounded-2xl">
                                            <FaThumbsUp className="h-6 w-6 text-yellow-600 mx-auto mb-2" />
                                            <div className="text-2xl font-bold text-gray-800">4.9</div>
                                            <div className="text-xs text-gray-600">Rating</div>
                                        </div>
                                    </div>

                                    {/* CTA Button */}
                                    <button
                                        onClick={() => setIsBookingModalOpen(true)}
                                        className="w-full group relative overflow-hidden bg-gradient-to-r from-primary via-purple-600 to-pink-600 text-white font-bold py-4 px-8 rounded-2xl hover:shadow-2xl focus:outline-none focus:ring-4 focus:ring-primary/30 transition-all duration-500 transform hover:scale-105"
                                    >
                                        <div className="absolute inset-0 bg-gradient-to-r from-pink-600 via-purple-600 to-primary opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                        <div className="relative flex items-center justify-center gap-3">
                                            <FaSparkles className="h-5 w-5" />
                                            <span className="text-lg">Book Now</span>
                                            <FaArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                                        </div>
                                    </button>

                                    <p className="text-center text-sm text-gray-500 mt-4">
                                        🎉 Special offers available for new customers!
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Modern Membership Plans Section */}
            <div className="relative py-20 sm:py-32">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Section Header */}
                    <div className="text-center mb-16">
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary to-purple-600 rounded-2xl mb-6">
                            <FaCrown className="h-8 w-8 text-white" />
                        </div>
                        <h2 className="text-4xl sm:text-5xl lg:text-6xl font-black mb-6">
                            <span className="bg-gradient-to-r from-primary via-purple-600 to-pink-600 bg-clip-text text-transparent">
                                Membership Plans
                            </span>
                        </h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                            💎 Unlock exclusive benefits and save more with our premium membership plans
                        </p>
                    </div>

                    {/* Plans Grid */}
                    <div className="grid gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                        {plans.map((plan, index) => (
                            <div
                                key={plan.id}
                                className={`relative group ${index === 1 ? 'lg:scale-110 lg:z-10' : ''}`}
                            >
                                {/* Popular Badge */}
                                {index === 1 && (
                                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                                        <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
                                            🔥 Most Popular
                                        </div>
                                    </div>
                                )}

                                <div className="relative bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl hover:shadow-3xl border border-white/30 overflow-hidden transition-all duration-500 group-hover:-translate-y-2">
                                    {/* Plan Header */}
                                    <div className={`p-8 ${index === 1 ? 'bg-gradient-to-r from-primary/20 via-purple-500/20 to-pink-500/20' : 'bg-gradient-to-r from-gray-50 to-gray-100/50'}`}>
                                        <div className="flex items-center justify-between mb-4">
                                            <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                                            <div className="bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full">
                                                <span className="text-primary font-bold text-sm">{plan.validity_days} Days</span>
                                            </div>
                                        </div>

                                        <div className="flex items-baseline gap-2 mb-4">
                                            <span className="text-4xl font-black text-primary">₹{plan.price}</span>
                                            <span className="text-gray-500 text-lg">/plan</span>
                                        </div>

                                        <p className="text-gray-600 leading-relaxed">{plan.description}</p>
                                    </div>

                                    {/* Services List */}
                                    <div className="p-8">
                                        <div className="space-y-4 mb-8">
                                            <h4 className="font-bold text-gray-900 flex items-center text-lg">
                                                <FaGem className="mr-3 text-primary" />
                                                Included Services:
                                            </h4>
                                            {plan.services.map((service) => (
                                                <div key={service.id} className="flex items-center justify-between bg-gradient-to-r from-primary/5 to-purple/5 p-4 rounded-2xl border border-white/30">
                                                    <div className="flex items-center gap-3">
                                                        <FaCheck className="text-green-500 text-sm" />
                                                        <span className="text-gray-700 font-medium">{service.name}</span>
                                                    </div>
                                                    <div className="bg-primary/10 px-3 py-1 rounded-full">
                                                        <span className="text-primary font-bold text-sm">{service.allowed_count}x</span>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>

                                        {/* CTA Button */}
                                        <button
                                            onClick={() => {
                                                plan.services.forEach(service => {
                                                    const serviceDetails = services.find(s => s.id === service.id);
                                                    if (serviceDetails) {
                                                        for (let i = 0; i < service.allowed_count; i++) {
                                                            addToCart(serviceDetails);
                                                        }
                                                    }
                                                });
                                            }}
                                            className={`w-full group relative overflow-hidden font-bold py-4 px-6 rounded-2xl transition-all duration-500 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary/30 ${
                                                index === 1
                                                    ? 'bg-gradient-to-r from-primary via-purple-600 to-pink-600 text-white hover:shadow-2xl'
                                                    : 'bg-gradient-to-r from-gray-800 to-gray-900 text-white hover:from-primary hover:to-purple-600'
                                            }`}
                                        >
                                            <div className="relative flex items-center justify-center gap-3">
                                                <FaGift className="h-5 w-5" />
                                                <span>Choose This Plan</span>
                                                <FaArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Services Section - Updated with better styling */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                <div className="text-center mb-16">
                    <h2 className="text-4xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600 sm:text-5xl mb-4">
                        Our Services
                    </h2>
                    <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
                        Choose from our wide range of services
                    </p>
                </div>

                <div className="mt-12 grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                    {services.map((service) => (
                        <div
                            key={service.id}
                            className="bg-white rounded-3xl shadow-xl hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 overflow-hidden group border border-gray-100"
                        >
                            {service.image && (
                                <div className="h-64 w-full overflow-hidden">
                                    <img
                                        src={service.image}
                                        alt={service.name}
                                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                                    />
                                </div>
                            )}
                            <div className="p-8">
                                <h3 className="text-2xl font-bold text-gray-900 mb-3">{service.name}</h3>
                                <p className="text-gray-600 text-base leading-relaxed mb-6">{service.description}</p>
                                <div className="flex justify-between items-center pt-6 border-t border-gray-100">
                                    <div>
                                        <span className="text-2xl font-bold text-primary">
                                            ₹{service.price}
                                        </span>
                                        <span className="text-md text-gray-500 ml-3">
                                            ({service.duration_minutes} mins)
                                        </span>
                                    </div>
                                    <button
                                        onClick={() => addToCart(service)}
                                        disabled={selectedServices.some(s => s.id === service.id) || addedServices[service.id]}
                                        className={`inline-flex items-center px-6 py-3 text-base font-medium rounded-full transition-all duration-300 transform hover:scale-105 ${
                                            selectedServices.some(s => s.id === service.id) || addedServices[service.id]
                                                ? 'bg-green-500 text-white cursor-not-allowed'
                                                : 'bg-gradient-to-r from-primary to-purple-600 text-white hover:shadow-lg'
                                        }`}
                                    >
                                        {selectedServices.some(s => s.id === service.id) ? (
                                            <>
                                                <FaCheck className="mr-2 h-5 w-5" /> Added
                                            </>
                                        ) : addedServices[service.id] ? (
                                            <>
                                                <FaCheck className="mr-2 h-5 w-5" /> Added
                                            </>
                                        ) : (
                                            <>
                                                <FaPlusCircle className="mr-2 h-5 w-5" /> Add to Cart
                                            </>
                                        )}
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Booking Modal - Updated with better styling */}
            <Transition appear show={isBookingModalOpen} as={Fragment}>
                <Dialog
                    as="div"
                    className="fixed inset-0 z-50 overflow-y-auto"
                    onClose={() => setIsBookingModalOpen(false)}
                >
                    <div className="min-h-screen px-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0"
                            enterTo="opacity-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                        >
                            <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm" />
                        </Transition.Child>

                        <span className="inline-block h-screen align-middle" aria-hidden="true">
                            &#8203;
                        </span>

                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <div className="inline-block w-full max-w-3xl p-8 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-2xl rounded-2xl">
                                <Dialog.Title as="h3" className="text-3xl font-bold leading-6 text-gray-900 mb-8 border-b pb-6">
                                    Book Your Appointment
                                </Dialog.Title>

                                <div className="mt-6">
                                    <form onSubmit={handleSubmit} className="space-y-8">
                                        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
                                            <div>
                                                <label htmlFor="name" className="block text-lg font-medium text-gray-700 mb-2">
                                                    <FaUser className="inline-block mr-2 text-primary" />Name
                                                </label>
                                                <input
                                                    type="text"
                                                    id="name"
                                                    value={data.name}
                                                    onChange={e => setData('name', e.target.value)}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="Your full name"
                                                />
                                                {errors.name && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.name}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label htmlFor="email" className="block text-lg font-medium text-gray-700 mb-2">
                                                    <FaEnvelope className="inline-block mr-2 text-primary" />Email
                                                </label>
                                                <input
                                                    type="email"
                                                    id="email"
                                                    value={data.email}
                                                    onChange={e => setData('email', e.target.value)}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="<EMAIL>"
                                                />
                                                {errors.email && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.email}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label htmlFor="phone" className="block text-lg font-medium text-gray-700 mb-2">
                                                    <FaPhone className="inline-block mr-2 text-primary" />Phone
                                                </label>
                                                <input
                                                    type="tel"
                                                    id="phone"
                                                    value={data.phone}
                                                    onChange={e => setData('phone', e.target.value)}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="+****************"
                                                />
                                                {errors.phone && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.phone}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label htmlFor="password" className="block text-lg font-medium text-gray-700 mb-2">
                                                    Password (for new accounts)
                                                </label>
                                                <input
                                                    type="password"
                                                    id="password"
                                                    value={data.password}
                                                    onChange={e => setData('password', e.target.value)}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="Create a password"
                                                />
                                                {errors.password && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.password}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label htmlFor="appointment_date" className="block text-lg font-medium text-gray-700 mb-2">
                                                    <FaCalendarAlt className="inline-block mr-2 text-primary" />Date
                                                </label>
                                                <Flatpickr
                                                    id="appointment_date"
                                                    value={data.appointment_date}
                                                    onChange={(date) => {
                                                        if (date[0]) {
                                                            setData('appointment_date', date[0].toISOString().split('T')[0]);
                                                        }
                                                    }}
                                                    options={{
                                                        dateFormat: 'Y-m-d',
                                                        minDate: 'today',
                                                        disableMobile: true,
                                                    }}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="Select a date"
                                                />
                                                {errors.appointment_date && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.appointment_date}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label htmlFor="appointment_time" className="block text-lg font-medium text-gray-700 mb-2">
                                                    <FaClock className="inline-block mr-2 text-primary" />Time
                                                </label>
                                                <Flatpickr
                                                    id="appointment_time"
                                                    value={data.appointment_time}
                                                    onChange={(time) => {
                                                        if (time[0]) {
                                                            setData('appointment_time', time[0].toTimeString().slice(0, 5));
                                                        }
                                                    }}
                                                    options={{
                                                        enableTime: true,
                                                        noCalendar: true,
                                                        dateFormat: 'H:i',
                                                        time_24hr: true,
                                                        disableMobile: true,
                                                    }}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="Select a time"
                                                />
                                                {errors.appointment_time && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.appointment_time}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div>
                                            <label htmlFor="notes" className="block text-lg font-medium text-gray-700 mb-2">
                                                Notes
                                            </label>
                                            <textarea
                                                id="notes"
                                                rows={4}
                                                value={data.notes}
                                                onChange={e => setData('notes', e.target.value)}
                                                className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                placeholder="Any special requests or notes?"
                                            />
                                            {errors.notes && (
                                                <p className="mt-2 text-sm text-red-600">{errors.notes}</p>
                                            )}
                                        </div>

                                        <div className="mt-8 border-t pt-8 border-gray-100">
                                            <h4 className="text-2xl font-bold text-gray-900 mb-6">Your Selection</h4>
                                            <div className="space-y-4">
                                                {selectedServices.length === 0 ? (
                                                    <p className="text-gray-500 text-center py-8 text-lg">No services selected yet. Add some from below!</p>
                                                ) : (
                                                    selectedServices.map((service) => (
                                                        <div key={service.id} className="flex justify-between items-center bg-primary/5 p-6 rounded-xl hover:bg-primary/10 transition-colors duration-200">
                                                            <div>
                                                                <span className="font-semibold text-gray-800 text-xl">{service.name}</span>
                                                                <span className="text-sm text-gray-500 ml-3">
                                                                    ({service.duration_minutes} mins)
                                                                </span>
                                                            </div>
                                                            <div className="flex items-center">
                                                                <span className="text-primary font-bold text-xl mr-6">₹{service.price}</span>
                                                                <button
                                                                    type="button"
                                                                    onClick={() => removeFromCart(service.id)}
                                                                    className="text-red-500 hover:text-red-700 p-2 rounded-full hover:bg-red-50 transition-colors duration-200"
                                                                >
                                                                    <FaMinusCircle className="h-6 w-6" />
                                                                </button>
                                                            </div>
                                                        </div>
                                                    ))
                                                )}
                                            </div>
                                            <div className="mt-8 flex justify-between items-center border-t pt-6 border-gray-200">
                                                <div>
                                                    <span className="text-xl font-bold text-gray-700">Total Duration:</span>
                                                    <span className="ml-4 text-gray-800 text-xl">{calculateTotalDuration()} mins</span>
                                                </div>
                                                <div>
                                                    <span className="text-xl font-bold text-gray-700">Total Amount:</span>
                                                    <span className="ml-4 text-primary font-extrabold text-3xl">₹{calculateTotal()}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="mt-10 flex justify-end space-x-6">
                                            <button
                                                type="button"
                                                onClick={() => setIsBookingModalOpen(false)}
                                                className="inline-flex justify-center px-8 py-4 text-lg font-medium text-gray-700 bg-white border border-gray-300 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-300 transform hover:scale-105"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                disabled={processing || selectedServices.length === 0}
                                                className="inline-flex justify-center px-8 py-4 text-lg font-medium text-white bg-primary border border-transparent rounded-full hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105"
                                            >
                                                {processing ? 'Booking...' : 'Confirm Booking'}
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </Transition.Child>
                    </div>
                </Dialog>
            </Transition>
        </div>
    );
};

FrontPage.layout = 'blank';

export default FrontPage;

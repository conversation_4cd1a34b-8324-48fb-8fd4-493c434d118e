import React, { useState, Fragment } from 'react';
import { Link, useForm, router } from '@inertiajs/react';
import { Dialog, Transition } from '@headlessui/react';
import Flatpickr from 'react-flatpickr';
import 'flatpickr/dist/flatpickr.css';
import { FaCalendarAlt, FaClock, FaUser, FaEnvelope, FaPhone, FaDollarSign, FaMinusCircle, FaPlusCircle, FaShoppingCart, FaCheck, FaStar, FaMapMarkerAlt, FaInstagram, FaFacebook, FaTwitter, FaBuilding, FaTag } from 'react-icons/fa';

interface Service {
    id: number;
    name: string;
    description: string;
    duration_minutes: number;
    price: number;
    image: string;
}

interface Plan {
    id: number;
    name: string;
    description: string;
    price: number;
    validity_days: number;
    status: string;
    services: Array<{
        id: number;
        name: string;
        allowed_count: number;
    }>;
}

interface Branch {
    id: number;
    name: string;
    address: string;
}

interface Tenant {
    id: string;
    name: string;
    address: string;
    phone: string;
    email: string;
    logo: string;
    branches: Branch[];
}

interface PageProps {
    tenant: Tenant;
    services: Service[];
    plans: Plan[];
    selectedBranchId: number;
    layout: string;
}

interface FormData {
    name: string;
    email: string;
    phone: string;
    appointment_date: string;
    appointment_time: string;
    services: Service[];
    password: string;
    notes: string;
    branch_id: number;
}

interface FormErrors {
    name?: string;
    email?: string;
    phone?: string;
    appointment_date?: string;
    appointment_time?: string;
    password?: string;
    notes?: string;
    branch_id?: string;
    [key: string]: string | undefined;
}

const FrontPage = ({ tenant, services, plans, selectedBranchId, layout = 'blank' }: PageProps) => {
    const [selectedServices, setSelectedServices] = useState<Service[]>([]);
    const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
    const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
    const [addedServices, setAddedServices] = useState<Record<number, boolean>>({});
    const [showCart, setShowCart] = useState(false);
    const [selectedBranch, setSelectedBranch] = useState<number>(selectedBranchId);

    const { data, setData, post, processing, errors, reset } = useForm<FormData>({
        name: '',
        email: '',
        phone: '',
        appointment_date: '',
        appointment_time: '',
        services: [],
        password: '',
        notes: '',
        branch_id: selectedBranch,
    });

    // Handle branch selection
    const handleBranchChange = (branchId: number) => {
        setSelectedBranch(branchId);
        setSelectedServices([]); // Clear selected services when branch changes
        setShowCart(false); // Hide cart when branch changes
        
        // Update URL and reload data
        router.get(
            window.location.pathname,
            { branch_id: branchId },
            { preserveState: true, preserveScroll: true }
        );
    };

    const addToCart = (service: Service) => {
        if (!selectedServices.find(s => s.id === service.id)) {
            setSelectedServices([...selectedServices, service]);
            setAddedServices({ ...addedServices, [service.id]: true });
            setShowCart(true);
            
            setTimeout(() => {
                setAddedServices(prev => ({ ...prev, [service.id]: false }));
            }, 2000);
        }
    };

    const removeFromCart = (serviceId: number) => {
        setSelectedServices(selectedServices.filter(s => s.id !== serviceId));
    };

    const calculateTotal = () => {
        return selectedServices.reduce((sum, service) => sum + service.price, 0);
    };

    const calculateTotalDuration = () => {
        return selectedServices.reduce((sum, service) => sum + service.duration_minutes, 0);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const formData = {
            ...data,
            services: selectedServices,
            branch_id: selectedBranch,
        };
        post('/book', formData);
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-primary/5 via-white to-primary/5 font-sans">
            {/* Branch Selection */}
            {tenant.branches.length > 1 && (
                <div className="bg-white shadow-lg py-4">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between">
                            <h2 className="text-lg font-semibold text-gray-900">Select Branch</h2>
                            <select
                                value={selectedBranch}
                                onChange={(e) => handleBranchChange(Number(e.target.value))}
                                className="mt-1 block w-64 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
                            >
                                {tenant.branches.map((branch) => (
                                    <option key={branch.id} value={branch.id}>
                                        {branch.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </div>
            )}

            {/* Sticky Cart Popup */}
            <Transition
                show={showCart}
                enter="transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transition ease-in duration-300"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
            >
                <div className="fixed right-0 top-1/4 z-40 bg-white shadow-2xl rounded-l-2xl p-6 w-96 transform hover:scale-105 transition-transform duration-300">
                    <div className="flex items-center justify-between mb-6">
                        <h3 className="text-xl font-bold text-gray-900">Your Cart</h3>
                        <button
                            onClick={() => setShowCart(false)}
                            className="text-gray-500 hover:text-gray-700 transition-colors duration-200"
                        >
                            <FaMinusCircle className="h-6 w-6" />
                        </button>
                    </div>
                    <div className="space-y-4 max-h-96 overflow-y-auto custom-scrollbar">
                        {selectedServices.map((service) => (
                            <div key={service.id} className="flex items-center justify-between bg-primary/5 p-4 rounded-xl hover:bg-primary/10 transition-colors duration-200">
                                <div>
                                    <span className="font-semibold text-gray-800 text-lg">{service.name}</span>
                                    <span className="text-sm text-gray-500 ml-2">
                                        ({service.duration_minutes} mins)
                                    </span>
                                </div>
                                <div className="flex items-center">
                                    <span className="text-primary font-bold text-lg mr-4">₹{service.price}</span>
                                    <button
                                        onClick={() => removeFromCart(service.id)}
                                        className="text-red-500 hover:text-red-700 p-2 rounded-full hover:bg-red-50 transition-colors duration-200"
                                    >
                                        <FaMinusCircle className="h-5 w-5" />
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                    {selectedServices.length > 0 && (
                        <div className="mt-6 pt-6 border-t border-gray-200">
                            <div className="flex justify-between items-center mb-3">
                                <span className="font-semibold text-gray-700">Total Duration:</span>
                                <span className="text-gray-800 font-medium">{calculateTotalDuration()} mins</span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="font-semibold text-gray-700">Total Amount:</span>
                                <span className="text-primary font-bold text-2xl">₹{calculateTotal()}</span>
                            </div>
                        </div>
                    )}
                </div>
            </Transition>

            {/* Sticky Book Button */}
            {selectedServices.length > 0 && (
                <div className="fixed bottom-0 left-0 right-0 bg-white shadow-2xl z-40 p-6 transform transition-transform duration-300 hover:-translate-y-1">
                    <div className="max-w-7xl mx-auto flex justify-between items-center">
                        <div>
                            <span className="text-gray-600 text-lg">Selected {selectedServices.length} services</span>
                            <span className="ml-4 text-primary font-bold text-xl">Total: ₹{calculateTotal()}</span>
                        </div>
                        <button
                            onClick={() => setIsBookingModalOpen(true)}
                            className="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-primary border border-transparent rounded-full hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-300 transform hover:scale-105"
                        >
                            <FaCalendarAlt className="mr-3 h-6 w-6" />
                            Book Appointment
                        </button>
                    </div>
                </div>
            )}

            {/* Hero Section */}
            <div className="relative bg-white shadow-2xl overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 mix-blend-multiply" />
                <div className="relative max-w-7xl mx-auto py-24 px-4 sm:px-6 lg:px-8 lg:py-32">
                    <div className="lg:grid lg:grid-cols-2 lg:gap-12 lg:items-center">
                        <div className="sm:text-center lg:text-left">
                            <div className="flex items-center space-x-6 mb-8">
                                {tenant.logo && (
                                    <img
                                        src={tenant.logo}
                                        alt={`${tenant.name} logo`}
                                        className="h-24 w-24 rounded-2xl object-cover border-4 border-white shadow-xl transform hover:scale-105 transition-transform duration-300"
                                    />
                                )}
                                <div>
                                    <h1 className="text-5xl tracking-tight font-extrabold text-gray-900 sm:text-6xl md:text-7xl">
                                        <span className="block xl:inline text-primary">{tenant.name}</span>
                                    </h1>
                                    <div className="mt-4 flex items-center space-x-2">
                                        <div className="flex">
                                            {[1, 2, 3, 4, 5].map((star) => (
                                                <FaStar key={star} className="h-5 w-5 text-yellow-400" />
                                            ))}
                                        </div>
                                        <span className="text-gray-600">(4.9/5)</span>
                                    </div>
                                </div>
                            </div>
                            <div className="mt-8 space-y-6">
                                <div className="flex items-center text-gray-700 text-lg">
                                    <FaMapMarkerAlt className="h-6 w-6 text-primary mr-3" />
                                    <span>{tenant.address}</span>
                                </div>
                                <div className="flex items-center text-gray-700 text-lg">
                                    <FaPhone className="h-6 w-6 text-primary mr-3" />
                                    <span>{tenant.phone}</span>
                                </div>
                                <div className="flex items-center text-gray-700 text-lg">
                                    <FaEnvelope className="h-6 w-6 text-primary mr-3" />
                                    <span>{tenant.email}</span>
                                </div>
                                <div className="flex space-x-4 mt-6">
                                    <a href="#" className="text-gray-600 hover:text-primary transition-colors duration-200">
                                        <FaInstagram className="h-8 w-8" />
                                    </a>
                                    <a href="#" className="text-gray-600 hover:text-primary transition-colors duration-200">
                                        <FaFacebook className="h-8 w-8" />
                                    </a>
                                    <a href="#" className="text-gray-600 hover:text-primary transition-colors duration-200">
                                        <FaTwitter className="h-8 w-8" />
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div className="mt-12 lg:mt-0">
                            <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-8 shadow-xl">
                                <h2 className="text-3xl font-bold text-gray-900 mb-6">Book Your Appointment</h2>
                                <p className="text-gray-600 mb-8">Experience the best beauty services in town. Book your appointment now!</p>
                                <button
                                    onClick={() => setIsBookingModalOpen(true)}
                                    className="w-full inline-flex justify-center items-center px-8 py-4 text-lg font-semibold text-white bg-primary border border-transparent rounded-full hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-300 transform hover:scale-105"
                                >
                                    <FaCalendarAlt className="mr-3 h-6 w-6" />
                                    Book Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Plans Section */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                <div className="text-center mb-16">
                    <h2 className="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-4">
                        Our Membership Plans
                    </h2>
                    <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
                        Choose from our exclusive membership plans and enjoy special benefits!
                    </p>
                </div>

                <div className="mt-12 grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                    {plans.map((plan) => (
                        <div
                            key={plan.id}
                            className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 overflow-hidden"
                        >
                            <div className="p-8">
                                <div className="flex items-center justify-between mb-6">
                                    <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                                    <span className="bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-semibold">
                                        {plan.validity_days} Days
                                    </span>
                                </div>
                                <p className="text-gray-600 text-base leading-relaxed mb-6">{plan.description}</p>
                                
                                <div className="space-y-4 mb-8">
                                    <h4 className="font-semibold text-gray-900">Included Services:</h4>
                                    {plan.services.map((service) => (
                                        <div key={service.id} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                                            <span className="text-gray-700">{service.name}</span>
                                            <span className="text-primary font-medium">{service.allowed_count} times</span>
                                        </div>
                                    ))}
                                </div>

                                <div className="flex justify-between items-center pt-6 border-t border-gray-100">
                                    <div>
                                        <span className="text-3xl font-bold text-primary">
                                            <FaDollarSign className="inline-block mr-1" />
                                            {plan.price}
                                        </span>
                                    </div>
                                    <button
                                        onClick={() => {
                                            // Add plan services to cart
                                            plan.services.forEach(service => {
                                                const serviceDetails = services.find(s => s.id === service.id);
                                                if (serviceDetails) {
                                                    for (let i = 0; i < service.allowed_count; i++) {
                                                        addToCart(serviceDetails);
                                                    }
                                                }
                                            });
                                        }}
                                        className="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-primary hover:bg-primary-dark transition-all duration-300 transform hover:scale-105"
                                    >
                                        <FaTag className="mr-2 h-5 w-5" />
                                        Add Plan to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Services Section */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                <div className="text-center mb-16">
                    <h2 className="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-4">
                        Our Services
                    </h2>
                    <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
                        Choose from our wide range of services
                    </p>
                </div>

                <div className="mt-12 grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                    {services.map((service) => (
                        <div
                            key={service.id}
                            className="bg-white rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 overflow-hidden group"
                        >
                            {service.image && (
                                <div className="h-64 w-full overflow-hidden">
                                    <img
                                        src={service.image}
                                        alt={service.name}
                                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                                    />
                                </div>
                            )}
                            <div className="p-8">
                                <h3 className="text-2xl font-bold text-gray-900 mb-3">{service.name}</h3>
                                <p className="text-gray-600 text-base leading-relaxed mb-6">{service.description}</p>
                                <div className="flex justify-between items-center pt-6 border-t border-gray-100">
                                    <div>
                                        <span className="text-2xl font-bold text-primary">
                                            <FaDollarSign className="inline-block mr-1" />
                                            {service.price}
                                        </span>
                                        <span className="text-md text-gray-500 ml-3">
                                            ({service.duration_minutes} mins)
                                        </span>
                                    </div>
                                    <button
                                        onClick={() => addToCart(service)}
                                        disabled={selectedServices.some(s => s.id === service.id) || addedServices[service.id]}
                                        className={`inline-flex items-center px-6 py-3 text-base font-medium rounded-full transition-all duration-300 transform hover:scale-105 ${
                                            selectedServices.some(s => s.id === service.id) || addedServices[service.id]
                                                ? 'bg-green-500 text-white cursor-not-allowed'
                                                : 'bg-primary text-white hover:bg-primary-dark'
                                        }`}
                                    >
                                        {selectedServices.some(s => s.id === service.id) ? (
                                            <>
                                                <FaCheck className="mr-2 h-5 w-5" /> Added
                                            </>
                                        ) : addedServices[service.id] ? (
                                            <>
                                                <FaCheck className="mr-2 h-5 w-5" /> Added
                                            </>
                                        ) : (
                                            <>
                                                <FaPlusCircle className="mr-2 h-5 w-5" /> Add to Cart
                                            </>
                                        )}
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Booking Modal */}
            <Transition appear show={isBookingModalOpen} as={Fragment}>
                <Dialog
                    as="div"
                    className="fixed inset-0 z-50 overflow-y-auto"
                    onClose={() => setIsBookingModalOpen(false)}
                >
                    <div className="min-h-screen px-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0"
                            enterTo="opacity-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                        >
                            <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm" />
                        </Transition.Child>

                        <span className="inline-block h-screen align-middle" aria-hidden="true">
                            &#8203;
                        </span>

                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <div className="inline-block w-full max-w-3xl p-8 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-2xl rounded-2xl">
                                <Dialog.Title as="h3" className="text-3xl font-bold leading-6 text-gray-900 mb-8 border-b pb-6">
                                    Book Your Appointment
                                </Dialog.Title>

                                <div className="mt-6">
                                    <form onSubmit={handleSubmit} className="space-y-8">
                                        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
                                            <div>
                                                <label htmlFor="name" className="block text-lg font-medium text-gray-700 mb-2">
                                                    <FaUser className="inline-block mr-2 text-primary" />Name
                                                </label>
                                                <input
                                                    type="text"
                                                    id="name"
                                                    value={data.name}
                                                    onChange={e => setData('name', e.target.value)}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="Your full name"
                                                />
                                                {errors.name && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.name}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label htmlFor="email" className="block text-lg font-medium text-gray-700 mb-2">
                                                    <FaEnvelope className="inline-block mr-2 text-primary" />Email
                                                </label>
                                                <input
                                                    type="email"
                                                    id="email"
                                                    value={data.email}
                                                    onChange={e => setData('email', e.target.value)}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="<EMAIL>"
                                                />
                                                {errors.email && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.email}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label htmlFor="phone" className="block text-lg font-medium text-gray-700 mb-2">
                                                    <FaPhone className="inline-block mr-2 text-primary" />Phone
                                                </label>
                                                <input
                                                    type="tel"
                                                    id="phone"
                                                    value={data.phone}
                                                    onChange={e => setData('phone', e.target.value)}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="+****************"
                                                />
                                                {errors.phone && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.phone}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label htmlFor="password" className="block text-lg font-medium text-gray-700 mb-2">
                                                    Password (for new accounts)
                                                </label>
                                                <input
                                                    type="password"
                                                    id="password"
                                                    value={data.password}
                                                    onChange={e => setData('password', e.target.value)}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="Create a password"
                                                />
                                                {errors.password && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.password}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label htmlFor="appointment_date" className="block text-lg font-medium text-gray-700 mb-2">
                                                    <FaCalendarAlt className="inline-block mr-2 text-primary" />Date
                                                </label>
                                                <Flatpickr
                                                    id="appointment_date"
                                                    value={data.appointment_date}
                                                    onChange={(date) => {
                                                        if (date[0]) {
                                                            setData('appointment_date', date[0].toISOString().split('T')[0]);
                                                        }
                                                    }}
                                                    options={{
                                                        dateFormat: 'Y-m-d',
                                                        minDate: 'today',
                                                        disableMobile: true,
                                                    }}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="Select a date"
                                                />
                                                {errors.appointment_date && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.appointment_date}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label htmlFor="appointment_time" className="block text-lg font-medium text-gray-700 mb-2">
                                                    <FaClock className="inline-block mr-2 text-primary" />Time
                                                </label>
                                                <Flatpickr
                                                    id="appointment_time"
                                                    value={data.appointment_time}
                                                    onChange={(time) => {
                                                        if (time[0]) {
                                                            setData('appointment_time', time[0].toTimeString().slice(0, 5));
                                                        }
                                                    }}
                                                    options={{
                                                        enableTime: true,
                                                        noCalendar: true,
                                                        dateFormat: 'H:i',
                                                        time_24hr: true,
                                                        disableMobile: true,
                                                    }}
                                                    className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                    placeholder="Select a time"
                                                />
                                                {errors.appointment_time && (
                                                    <p className="mt-2 text-sm text-red-600">{errors.appointment_time}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div>
                                            <label htmlFor="notes" className="block text-lg font-medium text-gray-700 mb-2">
                                                Notes
                                            </label>
                                            <textarea
                                                id="notes"
                                                rows={4}
                                                value={data.notes}
                                                onChange={e => setData('notes', e.target.value)}
                                                className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-lg p-4"
                                                placeholder="Any special requests or notes?"
                                            />
                                            {errors.notes && (
                                                <p className="mt-2 text-sm text-red-600">{errors.notes}</p>
                                            )}
                                        </div>

                                        <div className="mt-8 border-t pt-8 border-gray-100">
                                            <h4 className="text-2xl font-bold text-gray-900 mb-6">Your Selection</h4>
                                            <div className="space-y-4">
                                                {selectedServices.length === 0 ? (
                                                    <p className="text-gray-500 text-center py-8 text-lg">No services selected yet. Add some from below!</p>
                                                ) : (
                                                    selectedServices.map((service) => (
                                                        <div key={service.id} className="flex justify-between items-center bg-primary/5 p-6 rounded-xl hover:bg-primary/10 transition-colors duration-200">
                                                            <div>
                                                                <span className="font-semibold text-gray-800 text-xl">{service.name}</span>
                                                                <span className="text-sm text-gray-500 ml-3">
                                                                    ({service.duration_minutes} mins)
                                                                </span>
                                                            </div>
                                                            <div className="flex items-center">
                                                                <span className="text-primary font-bold text-xl mr-6">₹{service.price}</span>
                                                                <button
                                                                    type="button"
                                                                    onClick={() => removeFromCart(service.id)}
                                                                    className="text-red-500 hover:text-red-700 p-2 rounded-full hover:bg-red-50 transition-colors duration-200"
                                                                >
                                                                    <FaMinusCircle className="h-6 w-6" />
                                                                </button>
                                                            </div>
                                                        </div>
                                                    ))
                                                )}
                                            </div>
                                            <div className="mt-8 flex justify-between items-center border-t pt-6 border-gray-200">
                                                <div>
                                                    <span className="text-xl font-bold text-gray-700">Total Duration:</span>
                                                    <span className="ml-4 text-gray-800 text-xl">{calculateTotalDuration()} mins</span>
                                                </div>
                                                <div>
                                                    <span className="text-xl font-bold text-gray-700">Total Amount:</span>
                                                    <span className="ml-4 text-primary font-extrabold text-3xl">₹{calculateTotal()}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="mt-10 flex justify-end space-x-6">
                                            <button
                                                type="button"
                                                onClick={() => setIsBookingModalOpen(false)}
                                                className="inline-flex justify-center px-8 py-4 text-lg font-medium text-gray-700 bg-white border border-gray-300 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-300 transform hover:scale-105"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                disabled={processing || selectedServices.length === 0}
                                                className="inline-flex justify-center px-8 py-4 text-lg font-medium text-white bg-primary border border-transparent rounded-full hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105"
                                            >
                                                {processing ? 'Booking...' : 'Confirm Booking'}
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </Transition.Child>
                    </div>
                </Dialog>
            </Transition>
        </div>
    );
};

FrontPage.layout = 'blank';

export default FrontPage;
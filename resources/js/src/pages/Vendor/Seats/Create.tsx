import { useEffect, useState } from 'react';
import { Link, useForm } from '@inertiajs/react';
import { useDispatch, useSelector } from 'react-redux';
import { IRootState } from '@/store';
import { setPageTitle } from '@/store/themeConfigSlice';

interface Props {
    staff: Array<{
        id: number;
        name: string;
    }>;
}

const Create = ({ staff }: Props) => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Create Seat'));
    });

    const { data, setData, post, processing, errors } = useForm({
        name: '',
        staff_id: '',
        status: 'available',
        notes: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/vendor/seats');
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/seats" className="text-primary hover:underline">
                        Seats
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Create</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Create New Seat</h5>
                    </div>

                    <form className="space-y-5" onSubmit={handleSubmit}>
                        <div>
                            <label htmlFor="name">Name</label>
                            <input
                                id="name"
                                type="text"
                                placeholder="Enter Seat Name"
                                className="form-input"
                                value={data.name}
                                onChange={(e) => setData('name', e.target.value)}
                            />
                            {errors.name && <div className="text-danger mt-1">{errors.name}</div>}
                        </div>

                        <div>
                            <label htmlFor="staff_id">Staff Member</label>
                            <select
                                id="staff_id"
                                className="form-select"
                                value={data.staff_id}
                                onChange={(e) => setData('staff_id', e.target.value)}
                            >
                                <option value="">Select Staff Member</option>
                                {staff.map((member) => (
                                    <option key={member.id} value={member.id}>
                                        {member.name}
                                    </option>
                                ))}
                            </select>
                            {errors.staff_id && <div className="text-danger mt-1">{errors.staff_id}</div>}
                        </div>

                        <div>
                            <label htmlFor="status">Status</label>
                            <select
                                id="status"
                                className="form-select"
                                value={data.status}
                                onChange={(e) => setData('status', e.target.value)}
                            >
                                <option value="available">Available</option>
                                <option value="occupied">Occupied</option>
                                <option value="cleaning">Cleaning</option>
                                <option value="maintenance">Maintenance</option>
                            </select>
                            {errors.status && <div className="text-danger mt-1">{errors.status}</div>}
                        </div>

                        <div>
                            <label htmlFor="notes">Notes</label>
                            <textarea
                                id="notes"
                                rows={3}
                                className="form-textarea resize-none min-h-[130px]"
                                placeholder="Enter Notes"
                                value={data.notes}
                                onChange={(e) => setData('notes', e.target.value)}
                            ></textarea>
                            {errors.notes && <div className="text-danger mt-1">{errors.notes}</div>}
                        </div>

                        <div className="flex justify-end items-center mt-8">
                            <Link href="/vendor/seats" className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary ltr:ml-4 rtl:mr-4" disabled={processing}>
                                {processing ? 'Creating...' : 'Create Seat'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Create; 
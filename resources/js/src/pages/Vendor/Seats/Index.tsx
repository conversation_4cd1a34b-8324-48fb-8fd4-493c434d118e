import { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch, useSelector } from 'react-redux';
import { IRootState } from '@/store';
import { setPageTitle } from '@/store/themeConfigSlice';
import PerfectScrollbar from 'react-perfect-scrollbar';
import Dropdown from '@/components/Dropdown';
import Swal from 'sweetalert2';

interface Props {
    seats: {
        data: Array<{
            id: number;
            name: string;
            status: string;
            staff: {
                id: number;
                name: string;
            } | null;
            notes: string | null;
        }>;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search: string;
        status: string;
        sort: string;
        direction: string;
    };
}

const Index = ({ seats, filters }: Props) => {
    const dispatch = useDispatch();
    const [search, setSearch] = useState(filters.search);
    const [status, setStatus] = useState(filters.status);

    useEffect(() => {
        dispatch(setPageTitle('Seat Management'));
    });

    const handleSearch = (value: string) => {
        setSearch(value);
        router.get(
            '/vendor/seats',
            { search: value, status, sort: filters.sort, direction: filters.direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleStatusChange = (value: string) => {
        setStatus(value);
        router.get(
            '/vendor/seats',
            { search, status: value, sort: filters.sort, direction: filters.direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleSort = (field: string) => {
        const direction = filters.sort === field && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            '/vendor/seats',
            { search, status, sort: field, direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleDelete = (id: number) => {
        Swal.fire({
            title: 'Are you sure?',
            html: 'Are you sure you want to delete this seat?<br>This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.delete(`/vendor/seats/${id}`, {
                    onSuccess: () => {
                        Swal.fire({
                            title: 'Deleted!',
                            text: 'Seat has been deleted.',
                            icon: 'success',
                            padding: '2em',
                            customClass: {
                                container: 'sweet-alerts'
                            }
                        });
                    },
                    onError: () => {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Something went wrong while deleting the seat.',
                            icon: 'error',
                            padding: '2em',
                            customClass: {
                                container: 'sweet-alerts'
                            }
                        });
                    },
                });
            }
        });
    };

    const isRtl = useSelector((state: IRootState) => state.themeConfig.rtlClass) === 'rtl' ? true : false;

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Seats</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="flex md:items-center md:flex-row flex-col mb-5 gap-5">
                        <div className="flex items-center gap-5 flex-1">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className="form-input py-2 ltr:pr-11 rtl:pl-11 peer"
                                    value={search}
                                    onChange={(e) => handleSearch(e.target.value)}
                                />
                                <button type="button" className="absolute ltr:right-[11px] rtl:left-[11px] top-1/2 -translate-y-1/2 peer-focus:text-primary">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="11.5" cy="11.5" r="9.5" stroke="currentColor" strokeWidth="1.5" opacity="0.5"></circle>
                                        <path d="M18.5 18.5L22 22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"></path>
                                    </svg>
                                </button>
                            </div>
                            <div className="flex items-center gap-5">
                                <select
                                    className="form-select"
                                    value={status}
                                    onChange={(e) => handleStatusChange(e.target.value)}
                                >
                                    <option value="">All Status</option>
                                    <option value="available">Available</option>
                                    <option value="occupied">Occupied</option>
                                    <option value="cleaning">Cleaning</option>
                                    <option value="maintenance">Maintenance</option>
                                </select>
                            </div>
                        </div>
                        <div className="flex items-center gap-5">
                            <Link href="/vendor/seats/create" className="btn btn-primary">
                                <svg className="w-5 h-5 ltr:mr-2 rtl:ml-2" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                                Add New Seat
                            </Link>
                        </div>
                    </div>

                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th onClick={() => handleSort('name')} className="cursor-pointer">
                                        Name {filters.sort === 'name' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th onClick={() => handleSort('status')} className="cursor-pointer">
                                        Status {filters.sort === 'status' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th>Staff</th>
                                    <th>Notes</th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {seats.data.map((seat) => (
                                    <tr key={seat.id}>
                                        <td>{seat.name}</td>
                                        <td>
                                            <span
                                                className={`badge badge-outline-${
                                                    seat.status === 'available'
                                                        ? 'success'
                                                        : seat.status === 'occupied'
                                                        ? 'warning'
                                                        : seat.status === 'cleaning'
                                                        ? 'info'
                                                        : 'danger'
                                                }`}
                                            >
                                                {seat.status.charAt(0).toUpperCase() + seat.status.slice(1)}
                                            </span>
                                        </td>
                                        <td>{seat.staff?.name || '-'}</td>
                                        <td>{seat.notes || '-'}</td>
                                        <td className="text-center">
                                            <div className="flex gap-4 items-center justify-center">
                                                <Link href={`/vendor/seats/${seat.id}/edit`} className="btn btn-sm btn-outline-primary">
                                                    Edit
                                                </Link>
                                                <button type="button" className="btn btn-sm btn-outline-danger" onClick={() => handleDelete(seat.id)}>
                                                    Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination */}
                    <div className="mt-6">
                        <div className="flex flex-wrap items-center justify-center gap-4">
                            {seats.links.map((link, i) => (
                                <Link
                                    key={i}
                                    href={link.url || '#'}
                                    className={`px-4 py-2 text-sm font-semibold rounded-md ${
                                        link.active
                                            ? 'bg-primary text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                    }`}
                                >
                                    {link.label === '&laquo; Previous' ? 'Previous' : 
                                     link.label === 'Next &raquo;' ? 'Next' : 
                                     link.label}
                                </Link>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Index; 
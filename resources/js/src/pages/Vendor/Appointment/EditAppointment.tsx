import React, { useEffect } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import Swal from 'sweetalert2';
import { usePage } from '@inertiajs/react';
import Flatpickr from 'react-flatpickr';
import 'flatpickr/dist/flatpickr.css';

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
}

interface AppointmentService {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
    seat_id?: number;
}

interface Customer {
    id: number;
    name: string;
    email: string;
    phone: string;
}

interface Seat {
    id: number;
    name: string;
}

interface PageProps {
    services: Service[];
    customers: Customer[];
    allSeats: Seat[];
    appointment: {
        id: number;
        user_id: number;
        appointment_date: string;
        appointment_time: string;
        ticket_number: string;
        status: string;
        notes: string;
        services: AppointmentService[];
    };
    [key: string]: any;
}

const EditAppointment = () => {
    const dispatch = useDispatch();
    const { services, customers, appointment, allSeats } = usePage<PageProps>().props;

    useEffect(() => {
        dispatch(setPageTitle('Edit Appointment'));
    });

    const validationSchema = Yup.object().shape({
        user_id: Yup.number().required('Please select a customer'),
        appointment_date: Yup.string()
            .required('Please select an appointment date')
            .test('is-valid-date', 'Appointment date must be today or later', function(value) {
                if (!value) return false;
                const [year, month, day] = value.split('-').map(Number);
                const selectedDate = new Date(year, month - 1, day);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                return selectedDate >= today;
            }),
        appointment_time: Yup.string().required('Please select an appointment time'),
        services: Yup.array()
            .of(
                Yup.object().shape({
                    id: Yup.number().required(),
                    name: Yup.string().required(),
                    price: Yup.number().required(),
                })
            )
            .min(1, 'Please select at least one service')
            .required('Please select at least one service'),
        notes: Yup.string().max(500, 'Notes cannot exceed 500 characters'),
        status: Yup.string()
            .oneOf(['pending', 'in_progress', 'completed', 'cancelled'], 'Invalid status')
            .required('Please select a status'),
        seat_id: Yup.number().when('status', {
            is: (status: string) => status !== 'pending',
            then: (schema) => schema.required('Please select a seat'),
        }),
    });

    const initialValues = {
        user_id: appointment.user_id,
        appointment_date: appointment.appointment_date,
        appointment_time: appointment.appointment_time,
        services: appointment.services,
        notes: appointment.notes || '',
        status: appointment.status,
        seat_id: appointment.services[0]?.seat_id || '',
    };

    const handleSubmit = (values: any) => {
        router.put(`/vendor/appointments/${appointment.id}`, values, {
            onSuccess: () => {
                Swal.fire({
                    icon: 'success',
                    title: 'Appointment updated successfully',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                });
            },
            onError: (errors) => {
                    const errorValues = Object.values(errors);
                    const firstError =
                    typeof errorValues[0] === 'string'
                        ? errorValues[0]
                        : errorValues[0]?.[0] || 'Error updating appointment';

                    Swal.fire({
                    icon: 'error',
                    title: firstError,
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    });
            },
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/appointments" className="text-primary hover:underline">
                        Appointments
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Edit Appointment</span>
                </li>
            </ul>

            <div className="panel mt-6">
                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                >
                    {({ errors, touched, values, setFieldValue }) => (
                        <Form className="space-y-5">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                {/* Customer Selection */}
                                <div>
                                    <label htmlFor="user_id">Customer</label>
                                    <select
                                        id="user_id"
                                        name="user_id"
                                        className="form-select"
                                        value={values.user_id}
                                        onChange={(e) => setFieldValue('user_id', e.target.value)}
                                    >
                                        <option value="">Select Customer</option>
                                        {customers.map((customer) => (
                                            <option key={customer.id} value={customer.id}>
                                                {customer.name} ({customer.email})
                                            </option>
                                        ))}
                                    </select>
                                    {errors.user_id && touched.user_id && (
                                        <div className="text-danger mt-1">{errors.user_id}</div>
                                    )}
                                </div>

                                {/* Appointment Date */}
                                <div>
                                    <label htmlFor="appointment_date">Appointment Date</label>
                                    <Flatpickr
                                        id="appointment_date"
                                        name="appointment_date"
                                        className="form-input"
                                        value={values.appointment_date}
                                        onChange={(date) => {
                                            if (date[0]) {
                                                const year = date[0].getFullYear();
                                                const month = String(date[0].getMonth() + 1).padStart(2, '0');
                                                const day = String(date[0].getDate()).padStart(2, '0');
                                                const formattedDate = `${year}-${month}-${day}`;
                                                setFieldValue('appointment_date', formattedDate);
                                            }
                                        }}
                                        options={{
                                            dateFormat: 'Y-m-d',
                                            minDate: 'today',
                                            disableMobile: true,
                                            time_24hr: true,
                                            enableTime: false,
                                        }}
                                    />
                                    {errors.appointment_date && touched.appointment_date && (
                                        <div className="text-danger mt-1">{errors.appointment_date}</div>
                                    )}
                                </div>

                                {/* Appointment Time */}
                                <div>
                                    <label htmlFor="appointment_time">Appointment Time</label>
                                    <Flatpickr
                                        id="appointment_time"
                                        name="appointment_time"
                                        className="form-input"
                                        value={values.appointment_time}
                                        onChange={(time) => {
                                            if (time[0]) {
                                                setFieldValue('appointment_time', time[0].toTimeString().slice(0, 5));
                                            }
                                        }}
                                        options={{
                                            enableTime: true,
                                            noCalendar: true,
                                            dateFormat: 'H:i',
                                            time_24hr: true,
                                            disableMobile: true,
                                        }}
                                    />
                                    {errors.appointment_time && touched.appointment_time && (
                                        <div className="text-danger mt-1">{errors.appointment_time}</div>
                                    )}
                                </div>

                                {/* Services Selection */}
                                <div className="md:col-span-2">
                                    <label>Services</label>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                                        {services.map((service) => {
                                            // Find if this service was in the original appointment
                                            const originalService = appointment.services.find(s => s.id === service.id);
                                            const displayName = originalService ? originalService.name : service.name;
                                            const displayPrice = originalService ? originalService.price : service.price;
                                            
                                            return (
                                                <div key={service.id} className="flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        id={`service-${service.id}`}
                                                        className="form-checkbox"
                                                        checked={values.services.some((s) => s.id === service.id)}
                                                        onChange={(e) => {
                                                            const newServices = e.target.checked
                                                                ? [...values.services, {
                                                                    id: service.id,
                                                                    name: displayName,
                                                                    price: displayPrice,
                                                                    duration_minutes: service.duration_minutes
                                                                  }]
                                                                : values.services.filter((s) => s.id !== service.id);
                                                            setFieldValue('services', newServices);
                                                        }}
                                                    />
                                                    <label htmlFor={`service-${service.id}`} className="ml-2">
                                                        {displayName} - ₹{displayPrice}
                                                    </label>
                                                </div>
                                            );
                                        })}
                                    </div>
                                    {errors.services && touched.services && (
                                        <div className="text-danger mt-1">
                                            {typeof errors.services === 'string' ? errors.services : 'Please select at least one service'}
                                        </div>
                                    )}
                                </div>

                                {/* Status Selection */}
                                <div>
                                    <label htmlFor="status">Status</label>
                                    <select
                                        id="status"
                                        name="status"
                                        className="form-select"
                                        value={values.status}
                                        onChange={(e) => setFieldValue('status', e.target.value)}
                                    >
                                        <option value="pending">Pending</option>
                                        <option value="in_progress">In Progress</option>
                                        <option value="completed">Completed</option>
                                        <option value="cancelled">Cancelled</option>
                                    </select>
                                    {errors.status && touched.status && (
                                        <div className="text-danger mt-1">{errors.status}</div>
                                    )}
                                </div>

                                {/* Seat Selection */}
                                <div>
                                    <label htmlFor="seat_id">Select Seat</label>
                                    <select
                                        id="seat_id"
                                        name="seat_id"
                                        className="form-select"
                                        value={values.seat_id}
                                        onChange={(e) => setFieldValue('seat_id', e.target.value)}
                                    >
                                        <option value="">Select Seat</option>
                                        {allSeats.map((seat) => (
                                            <option key={seat.id} value={seat.id}>
                                                {seat.name}
                                            </option>
                                        ))}
                                    </select>
                                    {errors.seat_id && touched.seat_id && (
                                        <div className="text-danger mt-1">{errors.seat_id}</div>
                                    )}
                                </div>

                                {/* Notes */}
                                <div className="md:col-span-2">
                                    <label htmlFor="notes">Notes</label>
                                    <textarea
                                        id="notes"
                                        name="notes"
                                        className="form-textarea"
                                        rows={4}
                                        value={values.notes}
                                        onChange={(e) => setFieldValue('notes', e.target.value)}
                                    ></textarea>
                                    {errors.notes && touched.notes && (
                                        <div className="text-danger mt-1">{errors.notes}</div>
                                    )}
                                </div>
                            </div>

                            <div className="flex justify-end gap-4 mt-6">
                                <Link href="/vendor/appointments" className="btn btn-outline-danger">
                                    Cancel
                                </Link>
                                <button type="submit" className="btn btn-primary">
                                    Update Appointment
                                </button>
                            </div>
                        </Form>
                    )}
                </Formik>
            </div>
        </div>
    );
};

export default EditAppointment; 
import React, { useEffect } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import Swal from 'sweetalert2';
import { usePage } from '@inertiajs/react';
import Flatpickr from 'react-flatpickr';
import 'flatpickr/dist/flatpickr.css';

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
}

interface Customer {
    id: number;
    name: string;
    email: string;
    phone: string;
}

interface Seat {
    id: number;
    name: string;
}

interface PageProps {
    services: Service[];
    customers: Customer[];
    allSeats: Seat[];
    [key: string]: any;
}

interface FormValues {
    user_id: string;
    appointment_date: string;
    appointment_time: string;
    services: Service[];
    notes: string;
}

const AddAppointment = () => {
    const dispatch = useDispatch();
    const { services, customers, allSeats } = usePage<PageProps>().props;

    useEffect(() => {
        dispatch(setPageTitle('Add Appointment'));
    });

    const validationSchema = Yup.object().shape({
        user_id: Yup.number().required('Please select a customer'),
        appointment_date: Yup.string()
            .required('Please select an appointment date')
            .test('is-valid-date', 'Appointment date must be today or later', function(value) {
                if (!value) return false;
                const [year, month, day] = value.split('-').map(Number);
                const selectedDate = new Date(year, month - 1, day);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                return selectedDate >= today;
            }),
        appointment_time: Yup.string().required('Please select an appointment time'),
        services: Yup.array()
            .of(
                Yup.object().shape({
                    id: Yup.number().required(),
                    name: Yup.string().required(),
                    price: Yup.number().required(),
                })
            )
            .min(1, 'Please select at least one service')
            .required('Please select at least one service'),
        notes: Yup.string().max(500, 'Notes cannot exceed 500 characters'),
    });

    const initialValues: FormValues = {
        user_id: '',
        appointment_date: '',
        appointment_time: '',
        services: [],
        notes: '',
    };

    const handleSubmit = (values: any) => {
        router.post('/vendor/appointments', values, {
            onSuccess: () => {
                Swal.fire({
                    icon: 'success',
                    title: 'Appointment created successfully',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                });
            },
            onError: (errors) => {
                Swal.fire({
                    icon: 'error',
                    title: 'Error creating appointment',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                });
            },
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/appointments" className="text-primary hover:underline">
                        Appointments
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Add Appointment</span>
                </li>
            </ul>

            <div className="panel mt-6">
                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                >
                    {({ errors, touched, values, setFieldValue }) => (
                        <Form className="space-y-5">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                {/* Customer Selection */}
                                <div>
                                    <label htmlFor="user_id">Customer</label>
                                    <select
                                        id="user_id"
                                        name="user_id"
                                        className="form-select"
                                        value={values.user_id}
                                        onChange={(e) => setFieldValue('user_id', e.target.value)}
                                    >
                                        <option value="">Select Customer</option>
                                        {customers.map((customer) => (
                                            <option key={customer.id} value={customer.id}>
                                                {customer.name} ({customer.email})
                                            </option>
                                        ))}
                                    </select>
                                    {errors.user_id && touched.user_id && (
                                        <div className="text-danger mt-1">{errors.user_id}</div>
                                    )}
                                </div>

                                {/* Appointment Date */}
                                <div>
                                    <label htmlFor="appointment_date">Appointment Date</label>
                                    <Flatpickr
                                        id="appointment_date"
                                        name="appointment_date"
                                        className="form-input"
                                        value={values.appointment_date}
                                        onChange={(date) => {
                                            if (date[0]) {
                                                const year = date[0].getFullYear();
                                                const month = String(date[0].getMonth() + 1).padStart(2, '0');
                                                const day = String(date[0].getDate()).padStart(2, '0');
                                                const formattedDate = `${year}-${month}-${day}`;
                                                setFieldValue('appointment_date', formattedDate);
                                            }
                                        }}
                                        options={{
                                            dateFormat: 'Y-m-d',
                                            minDate: 'today',
                                            disableMobile: true,
                                            time_24hr: true,
                                            enableTime: false,
                                        }}
                                    />
                                    {errors.appointment_date && touched.appointment_date && (
                                        <div className="text-danger mt-1">{errors.appointment_date}</div>
                                    )}
                                </div>

                                {/* Appointment Time */}
                                <div>
                                    <label htmlFor="appointment_time">Appointment Time</label>
                                    <Flatpickr
                                        id="appointment_time"
                                        name="appointment_time"
                                        className="form-input"
                                        value={values.appointment_time}
                                        onChange={(time) => {
                                            if (time[0]) {
                                                setFieldValue('appointment_time', time[0].toTimeString().slice(0, 5));
                                            }
                                        }}
                                        options={{
                                            enableTime: true,
                                            noCalendar: true,
                                            dateFormat: 'H:i',
                                            time_24hr: true,
                                            disableMobile: true,
                                        }}
                                    />
                                    {errors.appointment_time && touched.appointment_time && (
                                        <div className="text-danger mt-1">{errors.appointment_time}</div>
                                    )}
                                </div>

                                {/* Services Selection */}
                                <div className="md:col-span-2">
                                    <label>Services</label>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                                        {services.map((service) => (
                                            <div key={service.id} className="flex items-center">
                                                <input
                                                    type="checkbox"
                                                    id={`service-${service.id}`}
                                                    className="form-checkbox"
                                                    checked={values.services.some((s) => s.id === service.id)}
                                                    onChange={(e) => {
                                                        const newServices = e.target.checked
                                                            ? [...values.services, service]
                                                            : values.services.filter((s) => s.id !== service.id);
                                                        setFieldValue('services', newServices);
                                                    }}
                                                />
                                                <label htmlFor={`service-${service.id}`} className="ml-2">
                                                    {service.name} - ₹{service.price}
                                                </label>
                                            </div>
                                        ))}
                                    </div>
                                    {errors.services && touched.services && (
                                        <div className="text-danger mt-1">
                                            {typeof errors.services === 'string' ? errors.services : 'Please select at least one service'}
                                        </div>
                                    )}
                                </div>

                                {/* Notes */}
                                <div className="md:col-span-2">
                                    <label htmlFor="notes">Notes</label>
                                    <textarea
                                        id="notes"
                                        name="notes"
                                        className="form-textarea"
                                        rows={4}
                                        value={values.notes}
                                        onChange={(e) => setFieldValue('notes', e.target.value)}
                                    ></textarea>
                                    {errors.notes && touched.notes && (
                                        <div className="text-danger mt-1">{errors.notes}</div>
                                    )}
                                </div>
                            </div>

                            <div className="flex justify-end gap-4 mt-6">
                                <Link href="/vendor/appointments" className="btn btn-outline-danger">
                                    Cancel
                                </Link>
                                <button type="submit" className="btn btn-primary">
                                    Create Appointment
                                </button>
                            </div>
                        </Form>
                    )}
                </Formik>
            </div>
        </div>
    );
};

export default AddAppointment;

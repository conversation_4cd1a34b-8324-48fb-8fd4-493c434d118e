
import { DataTable, DataTableSortStatus } from 'mantine-datatable';
import { useEffect, useState } from 'react';
import sortBy from 'lodash/sortBy';
import ReactApexChart from 'react-apexcharts';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';
import { Link } from '@inertiajs/react';
 
const Cancellations = () => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Cancellations'));
    });

    const rowData = [
        {
            id: 1,
            firstName: 'Caroline',
            lastName: '<PERSON>',
            email: '<EMAIL>',
            dob: '2004-05-28',
            address: {
                street: '529 Scholes Street',
                city: 'Temperanceville',
                zipcode: 5235,
                geo: {
                    lat: 23.806115,
                    lng: 164.677197,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 39,
            company: 'POLARAX',
        },
        {
            id: 2,
            firstName: 'Celeste',
            lastName: '<PERSON>',
            email: '<EMAIL>',
            dob: '1989-11-19',
            address: {
                street: '639 Kimball Street',
                city: 'Bascom',
                zipcode: 8907,
                geo: {
                    lat: 65.954483,
                    lng: 98.906478,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 32,
            company: 'MANGLO',
        },
        {
            id: 3,
            firstName: 'Tillman',
            lastName: 'Forbes',
            email: '<EMAIL>',
            dob: '2016-09-05',
            address: {
                street: '240 Vandalia Avenue',
                city: 'Thynedale',
                zipcode: 8994,
                geo: {
                    lat: -34.949388,
                    lng: -82.958111,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 26,
            company: 'APPLIDECK',
        },
        {
            id: 4,
            firstName: 'Daisy',
            lastName: 'Whitley',
            email: '<EMAIL>',
            dob: '1987-03-23',
            address: {
                street: '350 Pleasant Place',
                city: 'Idledale',
                zipcode: 9369,
                geo: {
                    lat: -54.458809,
                    lng: -127.476556,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 21,
            company: 'VOLAX',
        },
        {
            id: 5,
            firstName: 'Weber',
            lastName: 'Bowman',
            email: '<EMAIL>',
            dob: '1983-02-24',
            address: {
                street: '154 Conway Street',
                city: 'Broadlands',
                zipcode: 8131,
                geo: {
                    lat: 54.501351,
                    lng: -167.47138,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 26,
            company: 'ORBAXTER',
        },
        {
            id: 6,
            firstName: 'Buckley',
            lastName: 'Townsend',
            email: '<EMAIL>',
            dob: '2011-05-29',
            address: {
                street: '131 Guernsey Street',
                city: 'Vallonia',
                zipcode: 6779,
                geo: {
                    lat: -2.681655,
                    lng: 3.528942,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 40,
            company: 'OPPORTECH',
        },
        {
            id: 7,
            firstName: 'Latoya',
            lastName: 'Bradshaw',
            email: '<EMAIL>',
            dob: '2010-11-23',
            address: {
                street: '668 Lenox Road',
                city: 'Lowgap',
                zipcode: 992,
                geo: {
                    lat: 36.026423,
                    lng: 130.412198,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 24,
            company: 'GORGANIC',
        },
        {
            id: 8,
            firstName: 'Kate',
            lastName: 'Lindsay',
            email: '<EMAIL>',
            dob: '1987-07-02',
            address: {
                street: '773 Harrison Avenue',
                city: 'Carlton',
                zipcode: 5909,
                geo: {
                    lat: 42.464724,
                    lng: -12.948403,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 24,
            company: 'AVIT',
        },
        {
            id: 9,
            firstName: 'Marva',
            lastName: 'Sandoval',
            email: '<EMAIL>',
            dob: '2010-11-02',
            address: {
                street: '200 Malta Street',
                city: 'Tuskahoma',
                zipcode: 1292,
                geo: {
                    lat: -52.206169,
                    lng: 74.19452,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 28,
            company: 'QUILCH',
        },
        {
            id: 10,
            firstName: 'Decker',
            lastName: 'Russell',
            email: '<EMAIL>',
            dob: '1994-04-21',
            address: {
                street: '708 Bath Avenue',
                city: 'Coultervillle',
                zipcode: 1268,
                geo: {
                    lat: -41.550295,
                    lng: -146.598075,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 27,
            company: 'MEMORA',
        },
        {
            id: 11,
            firstName: 'Odom',
            lastName: 'Mills',
            email: '<EMAIL>',
            dob: '2010-01-24',
            address: {
                street: '907 Blake Avenue',
                city: 'Churchill',
                zipcode: 4400,
                geo: {
                    lat: -56.061694,
                    lng: -130.238523,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 34,
            company: 'ZORROMOP',
        },
        {
            id: 12,
            firstName: 'Sellers',
            lastName: 'Walters',
            email: '<EMAIL>',
            dob: '1975-11-12',
            address: {
                street: '978 Oakland Place',
                city: 'Gloucester',
                zipcode: 3802,
                geo: {
                    lat: 11.732587,
                    lng: 96.118099,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 28,
            company: 'ORBOID',
        },
        {
            id: 13,
            firstName: 'Wendi',
            lastName: 'Powers',
            email: '<EMAIL>',
            dob: '1979-06-02',
            address: {
                street: '376 Greenpoint Avenue',
                city: 'Elliott',
                zipcode: 9149,
                geo: {
                    lat: -78.159578,
                    lng: -9.835103,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 31,
            company: 'SNORUS',
        },
        {
            id: 14,
            firstName: 'Sophie',
            lastName: 'Horn',
            email: '<EMAIL>',
            dob: '2018-09-20',
            address: {
                street: '343 Doughty Street',
                city: 'Homestead',
                zipcode: 330,
                geo: {
                    lat: 65.484087,
                    lng: 137.413998,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 22,
            company: 'XTH',
        },
        {
            id: 15,
            firstName: 'Levine',
            lastName: 'Rodriquez',
            email: '<EMAIL>',
            dob: '1973-02-08',
            address: {
                street: '643 Allen Avenue',
                city: 'Weedville',
                zipcode: 8931,
                geo: {
                    lat: -63.185586,
                    lng: 117.327808,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 27,
            company: 'COMTRACT',
        },
        {
            id: 16,
            firstName: 'Little',
            lastName: 'Hatfield',
            email: '<EMAIL>',
            dob: '2012-01-03',
            address: {
                street: '194 Anthony Street',
                city: 'Williston',
                zipcode: 7456,
                geo: {
                    lat: 47.480837,
                    lng: 6.085909,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 33,
            company: 'ZIDANT',
        },
        {
            id: 17,
            firstName: 'Larson',
            lastName: 'Kelly',
            email: '<EMAIL>',
            dob: '2010-06-14',
            address: {
                street: '978 Indiana Place',
                city: 'Innsbrook',
                zipcode: 639,
                geo: {
                    lat: -71.766732,
                    lng: 150.854345,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 20,
            company: 'SUREPLEX',
        },
        {
            id: 18,
            firstName: 'Kendra',
            lastName: 'Molina',
            email: '<EMAIL>',
            dob: '2002-07-19',
            address: {
                street: '567 Charles Place',
                city: 'Kimmell',
                zipcode: 1966,
                geo: {
                    lat: 50.765816,
                    lng: -117.106499,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 31,
            company: 'DANJA',
        },
        {
            id: 19,
            firstName: 'Ebony',
            lastName: 'Livingston',
            email: '<EMAIL>',
            dob: '1994-10-18',
            address: {
                street: '284 Cass Place',
                city: 'Navarre',
                zipcode: 948,
                geo: {
                    lat: 65.271256,
                    lng: -83.064729,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 33,
            company: 'EURON',
        },
        {
            id: 20,
            firstName: 'Kaufman',
            lastName: 'Rush',
            email: '<EMAIL>',
            dob: '2011-07-10',
            address: {
                street: '408 Kingsland Avenue',
                city: 'Beaulieu',
                zipcode: 7911,
                geo: {
                    lat: 41.513153,
                    lng: 54.821641,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 39,
            company: 'ILLUMITY',
        },
        {
            id: 21,
            firstName: 'Frank',
            lastName: 'Hays',
            email: '<EMAIL>',
            dob: '2005-06-15',
            address: {
                street: '973 Caton Place',
                city: 'Dargan',
                zipcode: 4104,
                geo: {
                    lat: 63.314988,
                    lng: -138.771323,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 31,
            company: 'SYBIXTEX',
        },
        {
            id: 22,
            firstName: 'Carmella',
            lastName: 'Mccarty',
            email: '<EMAIL>',
            dob: '1980-03-06',
            address: {
                street: '919 Judge Street',
                city: 'Canby',
                zipcode: 8283,
                geo: {
                    lat: 9.198597,
                    lng: -138.809971,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 21,
            company: 'ZEDALIS',
        },
        {
            id: 23,
            firstName: 'Massey',
            lastName: 'Owen',
            email: '<EMAIL>',
            dob: '2012-03-01',
            address: {
                street: '108 Seaview Avenue',
                city: 'Slovan',
                zipcode: 3599,
                geo: {
                    lat: -74.648318,
                    lng: 99.620699,
                },
            },
            phone: '+****************',
            isActive: false,
            age: 40,
            company: 'DYNO',
        },
        {
            id: 24,
            firstName: 'Lottie',
            lastName: 'Lowery',
            email: '<EMAIL>',
            dob: '1982-10-10',
            address: {
                street: '557 Meserole Avenue',
                city: 'Fowlerville',
                zipcode: 4991,
                geo: {
                    lat: 54.811546,
                    lng: -20.996515,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 36,
            company: 'MULTIFLEX',
        },
        {
            id: 25,
            firstName: 'Addie',
            lastName: 'Luna',
            email: '<EMAIL>',
            dob: '1988-05-01',
            address: {
                street: '688 Bulwer Place',
                city: 'Harmon',
                zipcode: 7664,
                geo: {
                    lat: -12.762766,
                    lng: -39.924497,
                },
            },
            phone: '+****************',
            isActive: true,
            age: 32,
            company: 'PHARMACON',
        },
    ];

    const countryList = [
        { code: 'AE', name: 'United Arab Emirates' },
        { code: 'AR', name: 'Argentina' },
        { code: 'AT', name: 'Austria' },
        { code: 'AU', name: 'Australia' },
        { code: 'BE', name: 'Belgium' },
        { code: 'BG', name: 'Bulgaria' },
        { code: 'BN', name: 'Brunei' },
        { code: 'BR', name: 'Brazil' },
        { code: 'BY', name: 'Belarus' },
        { code: 'CA', name: 'Canada' },
        { code: 'CH', name: 'Switzerland' },
        { code: 'CL', name: 'Chile' },
        { code: 'CN', name: 'China' },
        { code: 'CO', name: 'Colombia' },
        { code: 'CZ', name: 'Czech Republic' },
        { code: 'DE', name: 'Germany' },
        { code: 'DK', name: 'Denmark' },
        { code: 'DZ', name: 'Algeria' },
        { code: 'EC', name: 'Ecuador' },
        { code: 'EG', name: 'Egypt' },
        { code: 'ES', name: 'Spain' },
        { code: 'FI', name: 'Finland' },
        { code: 'FR', name: 'France' },
        { code: 'GB', name: 'United Kingdom' },
        { code: 'GR', name: 'Greece' },
        { code: 'HK', name: 'Hong Kong' },
        { code: 'HR', name: 'Croatia' },
        { code: 'HU', name: 'Hungary' },
        { code: 'ID', name: 'Indonesia' },
        { code: 'IE', name: 'Ireland' },
        { code: 'IL', name: 'Israel' },
        { code: 'IN', name: 'India' },
        { code: 'IT', name: 'Italy' },
        { code: 'JO', name: 'Jordan' },
        { code: 'JP', name: 'Japan' },
        { code: 'KE', name: 'Kenya' },
        { code: 'KH', name: 'Cambodia' },
        { code: 'KR', name: 'South Korea' },
        { code: 'KZ', name: 'Kazakhstan' },
        { code: 'LA', name: 'Laos' },
        { code: 'LK', name: 'Sri Lanka' },
        { code: 'MA', name: 'Morocco' },
        { code: 'MM', name: 'Myanmar' },
        { code: 'MO', name: 'Macau' },
        { code: 'MX', name: 'Mexico' },
        { code: 'MY', name: 'Malaysia' },
        { code: 'NG', name: 'Nigeria' },
        { code: 'NL', name: 'Netherlands' },
        { code: 'NO', name: 'Norway' },
        { code: 'NZ', name: 'New Zealand' },
        { code: 'PE', name: 'Peru' },
        { code: 'PH', name: 'Philippines' },
        { code: 'PK', name: 'Pakistan' },
        { code: 'PL', name: 'Poland' },
        { code: 'PT', name: 'Portugal' },
        { code: 'QA', name: 'Qatar' },
        { code: 'RO', name: 'Romania' },
        { code: 'RS', name: 'Serbia' },
        { code: 'RU', name: 'Russia' },
        { code: 'SA', name: 'Saudi Arabia' },
        { code: 'SE', name: 'Sweden' },
        { code: 'SG', name: 'Singapore' },
        { code: 'SK', name: 'Slovakia' },
        { code: 'TH', name: 'Thailand' },
        { code: 'TN', name: 'Tunisia' },
        { code: 'TR', name: 'Turkey' },
        { code: 'TW', name: 'Taiwan' },
        { code: 'UK', name: 'Ukraine' },
        { code: 'UG', name: 'Uganda' },
        { code: 'US', name: 'United States' },
        { code: 'VN', name: 'Vietnam' },
        { code: 'ZA', name: 'South Africa' },
        { code: 'BA', name: 'Bosnia and Herzegovina' },
        { code: 'BD', name: 'Bangladesh' },
        { code: 'EE', name: 'Estonia' },
        { code: 'IQ', name: 'Iraq' },
        { code: 'LU', name: 'Luxembourg' },
        { code: 'LV', name: 'Latvia' },
        { code: 'MK', name: 'North Macedonia' },
        { code: 'SI', name: 'Slovenia' },
        { code: 'PA', name: 'Panama' },
    ];

    const [page, setPage] = useState(1);
    const PAGE_SIZES = [10, 20, 30, 50, 100];
    const [pageSize, setPageSize] = useState(PAGE_SIZES[0]);
    const [initialRecords, setInitialRecords] = useState(sortBy(rowData, 'id'));
    const [recordsData, setRecordsData] = useState(initialRecords);

    const [sortStatus, setSortStatus] = useState<DataTableSortStatus>({ columnAccessor: 'id', direction: 'asc' });

    useEffect(() => {
        setPage(1);
    }, [pageSize]);

    useEffect(() => {
        const from = (page - 1) * pageSize;
        const to = from + pageSize;
        setRecordsData([...initialRecords.slice(from, to)]);
    }, [page, pageSize, initialRecords]);

    useEffect(() => {
        const data = sortBy(initialRecords, sortStatus.columnAccessor);
        setInitialRecords(sortStatus.direction === 'desc' ? data.reverse() : data);
        setPage(1);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [sortStatus]);

    const randomColor = () => {
        const color = ['#4361ee', '#805dca', '#00ab55', '#e7515a', '#e2a03f', '#2196f3'];
        const random = Math.floor(Math.random() * color.length);
        return color[random];
    };

    const randomStatusColor = () => {
        const color = ['primary', 'secondary', 'success', 'danger', 'warning', 'info'];
        const random = Math.floor(Math.random() * color.length);
        return color[random];
    };

    const randomStatus = () => {
        const status = ['PAID', 'APPROVED', 'FAILED', 'CANCEL', 'SUCCESS', 'PENDING', 'COMPLETE'];
        const random = Math.floor(Math.random() * status.length);
        return status[random];
    };
    const getRandomNumber = (min: number, max: number) => {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    };

    const getCountry = () => {
        const random = Math.floor(Math.random() * countryList.length);
        return countryList[random];
    };

    const chart_options = () => {
        let option = {
            chart: { sparkline: { enabled: true } },
            stroke: { curve: 'smooth', width: 2 },
            markers: { size: [4, 7], strokeWidth: 0 },
            colors: [randomColor()],
            grid: { padding: { top: 5, bottom: 5 } },
            tooltip: {
                x: { show: false },
                y: {
                    title: {
                        formatter: () => {
                            return '';
                        },
                    },
                },
            },
        };
        return option;
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="#" className="text-primary hover:underline">
                        Forms
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Cancel Appointment</span>
                </li>
            </ul>
            <div className="mt-6 panel">
                <h5 className="mb-5 font-semibold text-lg dark:text-white-light">Cancel Appointment List</h5>
                <div className="datatables">
                    <DataTable
                        noRecordsText="No results match your search query"
                        highlightOnHover
                        className="whitespace-nowrap table-hover"
                        records={recordsData}
                        columns={[
                            { accessor: 'id', title: 'ID', sortable: true, render: ({ id }) => <strong className="text-info">#{id}</strong> },
                            {
                                accessor: 'firstName',
                                title: 'User',
                                sortable: true,
                                render: ({ firstName, lastName }) => (
                                    <div className="flex items-center gap-2">
                                        <img src={`/assets/images/profile-${getRandomNumber(1, 34)}.jpeg`} className="rounded-full w-9 max-w-none h-9" alt="user-profile" />
                                        <div className="font-semibold">{firstName + ' ' + lastName}</div>
                                    </div>
                                ),
                            },
                            {
                                accessor: 'country',
                                title: 'Country',
                                render: () => (
                                    <div className="flex items-center gap-2">
                                        <img width="24" src={`/assets/images/flags/${getCountry().code}.svg`} className="max-w-none" alt="flag" />
                                        <div>{getCountry().name}</div>
                                    </div>
                                ),
                            },
                            {
                                accessor: 'email',
                                title: 'Email',
                                sortable: true,
                                render: (record: any) => (
                                    <a href={`mailto:${record.email}`} className="text-primary hover:underline">
                                        {record.email}
                                    </a>
                                ),
                            },
                            {
                                accessor: 'age',
                                title: 'Progress',
                                render: () => (
                                    <div className="flex bg-[#ebedf2] dark:bg-dark/40 rounded-full w-4/5 min-w-[100px] h-2.5">
                                        <div
                                            className={`h-2.5 rounded-full rounded-bl-full text-center text-white text-xs bg-${randomStatusColor()}`}
                                            style={{ width: `${getRandomNumber(15, 100)}%` }}
                                        ></div>
                                    </div>
                                ),
                            },
                            { accessor: 'phone', title: 'Phone', sortable: true },
                            {
                                accessor: 'rating',
                                title: 'Rate',
                                titleClassName: '!text-center',
                                render: ({ id }) => (
                                    <div className="flex justify-center items-center text-warning">
                                        {Array.from(Array(getRandomNumber(1, 5)).keys()).map((i) => {
                                            return (
                                                <svg key={i + id} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 fill-warning">
                                                    <path
                                                        d="M9.15316 5.40838C10.4198 3.13613 11.0531 2 12 2C12.9469 2 13.5802 3.13612 14.8468 5.40837L15.1745 5.99623C15.5345 6.64193 15.7144 6.96479 15.9951 7.17781C16.2757 7.39083 16.6251 7.4699 17.3241 7.62805L17.9605 7.77203C20.4201 8.32856 21.65 8.60682 21.9426 9.54773C22.2352 10.4886 21.3968 11.4691 19.7199 13.4299L19.2861 13.9372C18.8096 14.4944 18.5713 14.773 18.4641 15.1177C18.357 15.4624 18.393 15.8341 18.465 16.5776L18.5306 17.2544C18.7841 19.8706 18.9109 21.1787 18.1449 21.7602C17.3788 22.3417 16.2273 21.8115 13.9243 20.7512L13.3285 20.4768C12.6741 20.1755 12.3469 20.0248 12 20.0248C11.6531 20.0248 11.3259 20.1755 10.6715 20.4768L10.0757 20.7512C7.77268 21.8115 6.62118 22.3417 5.85515 21.7602C5.08912 21.1787 5.21588 19.8706 5.4694 17.2544L5.53498 16.5776C5.60703 15.8341 5.64305 15.4624 5.53586 15.1177C5.42868 14.773 5.19043 14.4944 4.71392 13.9372L4.2801 13.4299C2.60325 11.4691 1.76482 10.4886 2.05742 9.54773C2.35002 8.60682 3.57986 8.32856 6.03954 7.77203L6.67589 7.62805C7.37485 7.4699 7.72433 7.39083 8.00494 7.17781C8.28555 6.96479 8.46553 6.64194 8.82547 5.99623L9.15316 5.40838Z"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                    ></path>
                                                </svg>
                                            );
                                        })}
                                    </div>
                                ),
                            },
                            {
                                accessor: 'series',
                                title: 'Progress',
                                render: ({ id }) => (
                                    <ReactApexChart
                                        key={id}
                                        type="line"
                                        series={[{ data: [21, 9, 36, 12, 44, 25, 59] }]}
                                        // @ts-ignore
                                        options={chart_options()}
                                        height={30}
                                        width={150}
                                    />
                                ),
                            },
                            {
                                accessor: 'status',
                                title: 'Status',
                                render: () => <span className={`badge badge-outline-${randomStatusColor()} `}>{randomStatus()}</span>,
                            },
                        ]}
                        totalRecords={initialRecords.length}
                        recordsPerPage={pageSize}
                        page={page}
                        onPageChange={(p) => setPage(p)}
                        recordsPerPageOptions={PAGE_SIZES}
                        onRecordsPerPageChange={setPageSize}
                        sortStatus={sortStatus}
                        onSortStatusChange={setSortStatus}
                        minHeight={200}
                        paginationText={({ from, to, totalRecords }) => `Showing  ${from} to ${to} of ${totalRecords} entries`}
                    />
                </div>
            </div>
        </div>
    );
};

export default Cancellations;

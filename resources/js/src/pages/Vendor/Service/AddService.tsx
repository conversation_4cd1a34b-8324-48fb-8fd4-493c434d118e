import React, { useEffect } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import Swal from 'sweetalert2';

const AddService = () => {
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(setPageTitle('Add Service'));
    });

    const validationSchema = Yup.object().shape({
        name: Yup.string().required('Name is required').max(255, 'Name cannot exceed 255 characters'),
        description: Yup.string().required('Description is required').max(1000, 'Description cannot exceed 1000 characters'),
        duration_minutes: Yup.number()
            .required('Duration is required')
            .min(5, 'Duration must be at least 5 minutes')
            .max(480, 'Duration cannot exceed 480 minutes'),
        price: Yup.number()
            .required('Price is required')
            .min(0, 'Price must be greater than or equal to 0'),
        is_active: Yup.boolean(),
    });

    const initialValues = {
        name: '',
        description: '',
        duration_minutes: 30,
        price: 0,
        is_active: true,
    };

    const handleSubmit = (values: any) => {
        router.post('/vendor/services', values, {
            onSuccess: () => {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Service has been created successfully.',
                    padding: '2em',
                    customClass: {
                        container: 'sweet-alerts'
                    }
                });
            },
            onError: (errors) => {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Something went wrong while creating the service.',
                    padding: '2em',
                    customClass: {
                        container: 'sweet-alerts'
                    }
                });
            },
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/services" className="text-primary hover:underline">
                        Services
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Add Service</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <Formik
                        initialValues={initialValues}
                        validationSchema={validationSchema}
                        onSubmit={handleSubmit}
                    >
                        {({ errors, touched, values, setFieldValue }) => (
                            <Form className="space-y-5">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                    <div>
                                        <label htmlFor="name">Service Name</label>
                                        <Field
                                            id="name"
                                            name="name"
                                            type="text"
                                            className="form-input"
                                            placeholder="Enter service name"
                                        />
                                        {errors.name && touched.name && (
                                            <div className="text-danger mt-1">{errors.name}</div>
                                        )}
                                    </div>

                                    <div>
                                        <label htmlFor="price">Price (₹)</label>
                                        <Field
                                            id="price"
                                            name="price"
                                            type="number"
                                            className="form-input"
                                            placeholder="Enter price"
                                        />
                                        {errors.price && touched.price && (
                                            <div className="text-danger mt-1">{errors.price}</div>
                                        )}
                                    </div>

                                    <div>
                                        <label htmlFor="duration_minutes">Duration (minutes)</label>
                                        <Field
                                            id="duration_minutes"
                                            name="duration_minutes"
                                            type="number"
                                            className="form-input"
                                            placeholder="Enter duration in minutes"
                                        />
                                        {errors.duration_minutes && touched.duration_minutes && (
                                            <div className="text-danger mt-1">{errors.duration_minutes}</div>
                                        )}
                                    </div>

                                    <div>
                                        <label htmlFor="is_active">Status</label>
                                        <div className="mt-2">
                                            <label className="inline-flex">
                                                <Field
                                                    type="checkbox"
                                                    name="is_active"
                                                    className="form-checkbox"
                                                />
                                                <span className="ltr:ml-2 rtl:mr-2">Active</span>
                                            </label>
                                        </div>
                                    </div>

                                    <div className="md:col-span-2">
                                        <label htmlFor="description">Description</label>
                                        <Field
                                            as="textarea"
                                            id="description"
                                            name="description"
                                            className="form-textarea"
                                            rows={4}
                                            placeholder="Enter service description"
                                        />
                                        {errors.description && touched.description && (
                                            <div className="text-danger mt-1">{errors.description}</div>
                                        )}
                                    </div>
                                </div>

                                <div className="flex justify-end gap-4 mt-6">
                                    <Link href="/vendor/services" className="btn btn-outline-danger">
                                        Cancel
                                    </Link>
                                    <button type="submit" className="btn btn-primary">
                                        Create Service
                                    </button>
                                </div>
                            </Form>
                        )}
                    </Formik>
                </div>
            </div>
        </div>
    );
};

export default AddService; 
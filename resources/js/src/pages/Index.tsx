import { useEffect, useState } from 'react';
import { Link } from '@inertiajs/react';
import { useDispatch, useSelector } from 'react-redux';
import { IRootState } from '../store';
import ReactApexChart from 'react-apexcharts';
import PerfectScrollbar from 'react-perfect-scrollbar';
import Dropdown from '../components/Dropdown';
import { setPageTitle } from '../store/themeConfigSlice';

const Index = () => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Salon Admin'));
    });
    const isDark = useSelector((state: IRootState) => state.themeConfig.theme === 'dark' || state.themeConfig.isDarkMode);
    const isRtl = useSelector((state: IRootState) => state.themeConfig.rtlClass) === 'rtl' ? true : false;

    const [loading] = useState(false);

    //Revenue Chart
    const revenueChart: any = {
        series: [
            {
                name: 'Income',
                data: [16800, 16800, 15500, 17800, 15500, 17000, 19000, 16000, 15000, 17000, 14000, 17000],
            },
            {
                name: 'Expenses',
                data: [16500, 17500, 16200, 17300, 16000, 19500, 16000, 17000, 16000, 19000, 18000, 19000],
            },
        ],
        options: {
            chart: {
                height: 325,
                type: 'area',
                fontFamily: 'Nunito, sans-serif',
                zoom: {
                    enabled: false,
                },
                toolbar: {
                    show: false,
                },
            },

            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                curve: 'smooth',
                width: 2,
                lineCap: 'square',
            },
            dropShadow: {
                enabled: true,
                opacity: 0.2,
                blur: 10,
                left: -7,
                top: 22,
            },
            colors: isDark ? ['#2196F3', '#E7515A'] : ['#1B55E2', '#E7515A'],
            markers: {
                discrete: [
                    {
                        seriesIndex: 0,
                        dataPointIndex: 6,
                        fillColor: '#1B55E2',
                        strokeColor: 'transparent',
                        size: 7,
                    },
                    {
                        seriesIndex: 1,
                        dataPointIndex: 5,
                        fillColor: '#E7515A',
                        strokeColor: 'transparent',
                        size: 7,
                    },
                ],
            },
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            xaxis: {
                axisBorder: {
                    show: false,
                },
                axisTicks: {
                    show: false,
                },
                crosshairs: {
                    show: true,
                },
                labels: {
                    offsetX: isRtl ? 2 : 0,
                    offsetY: 5,
                    style: {
                        fontSize: '12px',
                        cssClass: 'apexcharts-xaxis-title',
                    },
                },
            },
            yaxis: {
                tickAmount: 7,
                labels: {
                    formatter: (value: number) => {
                        return value / 1000 + 'K';
                    },
                    offsetX: isRtl ? -30 : -10,
                    offsetY: 0,
                    style: {
                        fontSize: '12px',
                        cssClass: 'apexcharts-yaxis-title',
                    },
                },
                opposite: isRtl ? true : false,
            },
            grid: {
                borderColor: isDark ? '#191E3A' : '#E0E6ED',
                strokeDashArray: 5,
                xaxis: {
                    lines: {
                        show: true,
                    },
                },
                yaxis: {
                    lines: {
                        show: false,
                    },
                },
                padding: {
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0,
                },
            },
            legend: {
                position: 'top',
                horizontalAlign: 'right',
                fontSize: '16px',
                markers: {
                    width: 10,
                    height: 10,
                    offsetX: -2,
                },
                itemMargin: {
                    horizontal: 10,
                    vertical: 5,
                },
            },
            tooltip: {
                marker: {
                    show: true,
                },
                x: {
                    show: false,
                },
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    inverseColors: !1,
                    opacityFrom: isDark ? 0.19 : 0.28,
                    opacityTo: 0.05,
                    stops: isDark ? [100, 100] : [45, 100],
                },
            },
        },
    };

    // totalVisitOptions
    const totalVisit: any = {
        series: [{ data: [21, 9, 36, 12, 44, 25, 59, 41, 66, 25] }],
        options: {
            chart: {
                height: 58,
                type: 'line',
                fontFamily: 'Nunito, sans-serif',
                sparkline: {
                    enabled: true,
                },
                dropShadow: {
                    enabled: true,
                    blur: 3,
                    color: '#009688',
                    opacity: 0.4,
                },
            },
            stroke: {
                curve: 'smooth',
                width: 2,
            },
            colors: ['#009688'],
            grid: {
                padding: {
                    top: 5,
                    bottom: 5,
                    left: 5,
                    right: 5,
                },
            },
            tooltip: {
                x: {
                    show: false,
                },
                y: {
                    title: {
                        formatter: () => {
                            return '';
                        },
                    },
                },
            },
        },
    };
    // paidVisitOptions
    const paidVisit: any = {
        series: [{ data: [22, 19, 30, 47, 32, 44, 34, 55, 41, 69] }],
        options: {
            chart: {
                height: 58,
                type: 'line',
                fontFamily: 'Nunito, sans-serif',
                sparkline: {
                    enabled: true,
                },
                dropShadow: {
                    enabled: true,
                    blur: 3,
                    color: '#e2a03f',
                    opacity: 0.4,
                },
            },
            stroke: {
                curve: 'smooth',
                width: 2,
            },
            colors: ['#e2a03f'],
            grid: {
                padding: {
                    top: 5,
                    bottom: 5,
                    left: 5,
                    right: 5,
                },
            },
            tooltip: {
                x: {
                    show: false,
                },
                y: {
                    title: {
                        formatter: () => {
                            return '';
                        },
                    },
                },
            },
        },
    };
    //Sales By Category
    const salesByCategory: any = {
        series: [985, 737, 270],
        options: {
            chart: {
                type: 'donut',
                height: 460,
                fontFamily: 'Nunito, sans-serif',
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 25,
                colors: isDark ? '#0e1726' : '#fff',
            },
            colors: isDark ? ['#5c1ac3', '#e2a03f', '#e7515a', '#e2a03f'] : ['#e2a03f', '#5c1ac3', '#e7515a'],
            legend: {
                position: 'bottom',
                horizontalAlign: 'center',
                fontSize: '14px',
                markers: {
                    width: 10,
                    height: 10,
                    offsetX: -2,
                },
                height: 50,
                offsetY: 20,
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '65%',
                        background: 'transparent',
                        labels: {
                            show: true,
                            name: {
                                show: true,
                                fontSize: '29px',
                                offsetY: -10,
                            },
                            value: {
                                show: true,
                                fontSize: '26px',
                                color: isDark ? '#bfc9d4' : undefined,
                                offsetY: 16,
                                formatter: (val: any) => {
                                    return val;
                                },
                            },
                            total: {
                                show: true,
                                label: 'Total',
                                color: '#888ea8',
                                fontSize: '29px',
                                formatter: (w: any) => {
                                    return w.globals.seriesTotals.reduce(function (a: any, b: any) {
                                        return a + b;
                                    }, 0);
                                },
                            },
                        },
                    },
                },
            },
            labels: ['Apparel', 'Sports', 'Others'],
            states: {
                hover: {
                    filter: {
                        type: 'none',
                        value: 0.15,
                    },
                },
                active: {
                    filter: {
                        type: 'none',
                        value: 0.15,
                    },
                },
            },
        },
    };

    //Daily Sales
    const dailySales: any = {
        series: [
            {
                name: 'Sales',
                data: [44, 55, 41, 67, 22, 43, 21],
            },
            {
                name: 'Last Week',
                data: [13, 23, 20, 8, 13, 27, 33],
            },
        ],
        options: {
            chart: {
                height: 160,
                type: 'bar',
                fontFamily: 'Nunito, sans-serif',
                toolbar: {
                    show: false,
                },
                stacked: true,
                stackType: '100%',
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 1,
            },
            colors: ['#e2a03f', '#e0e6ed'],
            responsive: [
                {
                    breakpoint: 480,
                    options: {
                        legend: {
                            position: 'bottom',
                            offsetX: -10,
                            offsetY: 0,
                        },
                    },
                },
            ],
            xaxis: {
                labels: {
                    show: false,
                },
                categories: ['Sun', 'Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat'],
            },
            yaxis: {
                show: false,
            },
            fill: {
                opacity: 1,
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '25%',
                },
            },
            legend: {
                show: false,
            },
            grid: {
                show: false,
                xaxis: {
                    lines: {
                        show: false,
                    },
                },
                padding: {
                    top: 10,
                    right: -20,
                    bottom: -20,
                    left: -20,
                },
            },
        },
    };

    //Total Orders
    const totalOrders: any = {
        series: [
            {
                name: 'Sales',
                data: [28, 40, 36, 52, 38, 60, 38, 52, 36, 40],
            },
        ],
        options: {
            chart: {
                height: 290,
                type: 'area',
                fontFamily: 'Nunito, sans-serif',
                sparkline: {
                    enabled: true,
                },
            },
            stroke: {
                curve: 'smooth',
                width: 2,
            },
            colors: isDark ? ['#00ab55'] : ['#00ab55'],
            labels: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
            yaxis: {
                min: 0,
                show: false,
            },
            grid: {
                padding: {
                    top: 125,
                    right: 0,
                    bottom: 0,
                    left: 0,
                },
            },
            fill: {
                opacity: 1,
                type: 'gradient',
                gradient: {
                    type: 'vertical',
                    shadeIntensity: 1,
                    inverseColors: !1,
                    opacityFrom: 0.3,
                    opacityTo: 0.05,
                    stops: [100, 100],
                },
            },
            tooltip: {
                x: {
                    show: false,
                },
            },
        },
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Salon Admin</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="gap-6 grid xl:grid-cols-3 mb-6">
                    <div className="xl:col-span-2 h-full panel">
                        <div className="flex justify-between items-center mb-5 dark:text-white-light">
                            <h5 className="font-semibold text-lg">Total Revenue </h5>
                            <div className="dropdown">
                                <Dropdown
                                    offset={[0, 1]}
                                    placement={`${isRtl ? 'bottom-start' : 'bottom-end'}`}
                                    button={
                                        <svg className="w-5 h-5 text-black/70 hover:!text-primary dark:text-white/70" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="5" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                            <circle opacity="0.5" cx="12" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                            <circle cx="19" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                        </svg>
                                    }
                                >
                                    <ul>
                                        <li>
                                            <button type="button">Weekly</button>
                                        </li>
                                        <li>
                                            <button type="button">Monthly</button>
                                        </li>
                                        <li>
                                            <button type="button">Yearly</button>
                                        </li>
                                    </ul>
                                </Dropdown>
                            </div>
                        </div>
                        <p className="text-lg dark:text-white-light/90">
                            This Month <span className="ml-2 text-primary">$10,840</span>
                        </p>
                        <div className="relative">
                            <div className="bg-white dark:bg-black rounded-lg overflow-hidden">
                                {loading ? (
                                    <div className="place-content-center grid bg-white-light/30 dark:bg-dark dark:bg-opacity-[0.08] min-h-[325px]">
                                        <span className="inline-flex border-2 dark:border-white border-black !border-l-transparent rounded-full w-5 h-5 animate-spin"></span>
                                    </div>
                                ) : (
                                    <ReactApexChart series={revenueChart.series} options={revenueChart.options} type="area" height={325} />
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="h-full panel">
                        <div className="flex items-center mb-5">
                            <h5 className="font-semibold text-lg dark:text-white-light">Sales By Product</h5>
                        </div>
                        <div>
                            <div className="bg-white dark:bg-black rounded-lg overflow-hidden">
                                {loading ? (
                                    <div className="place-content-center grid bg-white-light/30 dark:bg-dark dark:bg-opacity-[0.08] min-h-[325px]">
                                        <span className="inline-flex border-2 dark:border-white border-black !border-l-transparent rounded-full w-5 h-5 animate-spin"></span>
                                    </div>
                                ) : (
                                    <ReactApexChart series={salesByCategory.series} options={salesByCategory.options} type="donut" height={460} />
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                <div className="gap-6 grid sm:grid-cols-2 lg:grid-cols-3 mb-6">
                    <div className="sm:col-span-2 lg:col-span-1 h-full panel">
                        {/* statistics */}
                        <div className="flex justify-between mb-5 dark:text-white-light">
                            <h5 className="font-semibold text-lg">Total Appointments</h5>
                            <div className="dropdown">
                                <Dropdown
                                    offset={[0, 5]}
                                    placement={`${isRtl ? 'bottom-start' : 'bottom-end'}`}
                                    btnClassName="hover:text-primary"
                                    button={
                                        <svg className="w-5 h-5 text-black/70 hover:!text-primary dark:text-white/70" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="5" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                            <circle opacity="0.5" cx="12" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                            <circle cx="19" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                        </svg>
                                    }
                                >
                                    <ul>
                                        <li>
                                            <button type="button">This Week</button>
                                        </li>
                                        <li>
                                            <button type="button">Last Week</button>
                                        </li>
                                        <li>
                                            <button type="button">This Month</button>
                                        </li>
                                        <li>
                                            <button type="button">Last Month</button>
                                        </li>
                                    </ul>
                                </Dropdown>
                            </div>
                        </div>
                        <div className="gap-8 grid sm:grid-cols-2 font-bold text-[#515365] text-sm">
                            <div>
                                <div>
                                    <div>Total Visits</div>
                                    <div className="text-[#f8538d] text-lg">423,964</div>
                                </div>

                                <ReactApexChart series={totalVisit.series} options={totalVisit.options} type="line" height={58} className="overflow-hidden" />
                            </div>

                            <div>
                                <div>
                                    <div>Paid Visits</div>
                                    <div className="text-[#f8538d] text-lg">7,929</div>
                                </div>

                                <ReactApexChart series={paidVisit.series} options={paidVisit.options} type="line" height={58} className="overflow-hidden" />
                            </div>
                        </div>
                    </div>

                    <div className="h-full panel">
                        <div className="flex justify-between mb-5 dark:text-white-light">
                            <h5 className="font-semibold text-lg">New Clients</h5>

                            <div className="dropdown">
                                <Dropdown
                                    offset={[0, 5]}
                                    placement={`${isRtl ? 'bottom-start' : 'bottom-end'}`}
                                    btnClassName="hover:text-primary"
                                    button={
                                        <svg className="w-5 h-5 text-black/70 hover:!text-primary dark:text-white/70" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="5" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                            <circle opacity="0.5" cx="12" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                            <circle cx="19" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                        </svg>
                                    }
                                >
                                    <ul>
                                        <li>
                                            <button type="button">This Week</button>
                                        </li>
                                        <li>
                                            <button type="button">Last Week</button>
                                        </li>
                                        <li>
                                            <button type="button">This Month</button>
                                        </li>
                                        <li>
                                            <button type="button">Last Month</button>
                                        </li>
                                    </ul>
                                </Dropdown>
                            </div>
                        </div>
                        <div className="my-10 font-bold text-[#e95f2b] text-3xl">
                            <span>$ 45,141 </span>
                            <span className="ltr:mr-2 rtl:ml-2 text-black text-sm dark:text-white-light">this week</span>
                            <svg className="inline text-success" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    opacity="0.5"
                                    d="M22 7L14.6203 14.3347C13.6227 15.3263 13.1238 15.822 12.5051 15.822C11.8864 15.8219 11.3876 15.326 10.3902 14.3342L10.1509 14.0962C9.15254 13.1035 8.65338 12.6071 8.03422 12.6074C7.41506 12.6076 6.91626 13.1043 5.91867 14.0977L2 18"
                                    stroke="currentColor"
                                    strokeWidth="1.5"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                                <path d="M22.0001 12.5458V7H16.418" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                        </div>
                        <div className="flex justify-between items-center">
                            <div className="bg-dark-light dark:bg-dark-light/10 shadow-3xl dark:shadow-none p-1 rounded-full w-full h-5 overflow-hidden">
                                <div
                                    className="relative ltr:before:right-0.5 rtl:before:left-0.5 before:absolute before:inset-y-0 before:bg-white bg-gradient-to-r from-[#4361ee] to-[#805dca] before:m-auto rounded-full before:rounded-full w-full before:w-2 h-full before:h-2"
                                    style={{ width: '65%' }}
                                ></div>
                            </div>
                            <span className="rtl:mr-5 ltr:ml-5 dark:text-white-light">57%</span>
                        </div>
                    </div>

                    <div
                        className="before:top-0 before:-right-44 before:bottom-0 before:absolute content-between grid grid-cols-1 before:bg-[#1937cc] before:m-auto before:rounded-full before:w-96 h-full before:h-96 overflow-hidden panel"
                        style={{ background: 'linear-gradient(0deg,#00c6fb -227%,#005bea)' }}
                    >
                        <div className="z-[7] flex justify-between items-start mb-16 text-white-light">
                            <h5 className="font-semibold text-lg">Total Balance</h5>

                            <div className="relative text-xl whitespace-nowrap">
                                $ 41,741.42
                                <span className="bg-[#4361ee] mt-1 rtl:mr-auto ltr:ml-auto p-1 rounded text-[#d3d3d3] text-xs table">+ 2453</span>
                            </div>
                        </div>
                        <div className="z-10 flex justify-between items-center">
                            <div className="flex justify-between items-center">
                                <button type="button" className="place-content-center hover:bg-[#1937cc] shadow-[0_0_2px_0_#bfc9d4] ltr:mr-2 rtl:ml-2 p-1 rounded text-white-light">
                                    <svg className="w-5 h-5" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                </button>
                                <button type="button" className="place-content-center grid hover:bg-[#1937cc] shadow-[0_0_2px_0_#bfc9d4] p-1 rounded text-white-light">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M2 12C2 8.22876 2 6.34315 3.17157 5.17157C4.34315 4 6.22876 4 10 4H14C17.7712 4 19.6569 4 20.8284 5.17157C22 6.34315 22 8.22876 22 12C22 15.7712 22 17.6569 20.8284 18.8284C19.6569 20 17.7712 20 14 20H10C6.22876 20 4.34315 20 3.17157 18.8284C2 17.6569 2 15.7712 2 12Z"
                                            stroke="currentColor"
                                            strokeWidth="1.5"
                                        />
                                        <path opacity="0.5" d="M10 16H6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                        <path opacity="0.5" d="M14 16H12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                        <path opacity="0.5" d="M2 10L22 10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                    </svg>
                                </button>
                            </div>
                            <button type="button" className="z-10 hover:bg-[#4361ee] shadow-[0_0_2px_0_#bfc9d4] p-1 rounded text-white-light">
                                Upgrade
                            </button>
                        </div>
                    </div>
                </div>

                <div className="gap-6 grid sm:grid-cols-2 xl:grid-cols-3 mb-6">
                    <div className="sm:col-span-2 xl:col-span-1 pb-0 h-full panel">
                        <h5 className="mb-5 font-semibold text-lg dark:text-white-light">Recent Appoitment</h5>
                        <PerfectScrollbar className="relative ltr:-mr-3 mb-4 rtl:-ml-3 ltr:pr-3 rtl:pl-3 h-[290px]">
                            <div className="text-sm cursor-pointer">
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-primary ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Updated Server Logs</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">Just Now</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-primary-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-primary">
                                        Pending
                                    </span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-success ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Send Mail to HR and Admin</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">2 min ago</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-success-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-success">
                                        Completed
                                    </span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-danger ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Backup Files EOD</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">14:00</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-danger-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-danger">Pending</span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-black ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Collect documents from Sara</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">16:00</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-dark-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-dark">Completed</span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-warning ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Conference call with Marketing Manager.</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">17:00</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-warning-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-warning">
                                        In progress
                                    </span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-info ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Rebooted Server</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">17:00</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-info-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-info">Completed</span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-secondary ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Send contract details to Freelancer</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">18:00</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-secondary-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-secondary">
                                        Pending
                                    </span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-primary ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Updated Server Logs</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">Just Now</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-primary-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-primary">
                                        Pending
                                    </span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-success ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Send Mail to HR and Admin</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">2 min ago</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-success-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-success">
                                        Completed
                                    </span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-danger ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Backup Files EOD</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">14:00</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-danger-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-danger">Pending</span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-black ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Collect documents from Sara</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">16:00</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-dark-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-dark">Completed</span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-warning ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Conference call with Marketing Manager.</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">17:00</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-warning-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-warning">
                                        In progress
                                    </span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-info ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Rebooted Server</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">17:00</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-info-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-info">Completed</span>
                                </div>
                                <div className="group relative flex items-center py-1.5">
                                    <div className="bg-secondary ltr:mr-1 rtl:ml-1.5 rounded-full w-1.5 h-1.5"></div>
                                    <div className="flex-1">Send contract details to Freelancer</div>
                                    <div className="rtl:mr-auto ltr:ml-auto text-white-dark text-xs dark:text-gray-500">18:00</div>

                                    <span className="ltr:right-0 rtl:left-0 absolute bg-secondary-light dark:bg-black opacity-0 group-hover:opacity-100 text-xs badge badge-outline-secondary">
                                        Pending
                                    </span>
                                </div>
                            </div>
                        </PerfectScrollbar>
                        <div className="border-white-light dark:border-white/10 border-t">
                            <Link href="/" className="group group flex justify-center items-center p-4 font-semibold hover:text-primary">
                                View All
                                <svg
                                    className="rtl:mr-1 ltr:ml-1 w-4 h-4 transition group-hover:translate-x-1 rtl:group-hover:-translate-x-1 duration-300 rtl:rotate-180"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path d="M4 12H20M20 12L14 6M20 12L14 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                </svg>
                            </Link>
                        </div>
                    </div>
                    <div className="h-full panel">
                        <div className="flex justify-between items-center mb-5 dark:text-white-light">
                            <h5 className="font-semibold text-lg">Earnings per Staff</h5>
                            <div className="dropdown">
                                <Dropdown
                                    placement={`${isRtl ? 'bottom-start' : 'bottom-end'}`}
                                    button={
                                        <svg className="w-5 h-5 text-black/70 hover:!text-primary dark:text-white/70" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="5" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                            <circle opacity="0.5" cx="12" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                            <circle cx="19" cy="12" r="2" stroke="currentColor" strokeWidth="1.5" />
                                        </svg>
                                    }
                                >
                                    <ul>
                                        <li>
                                            <button type="button">View Report</button>
                                        </li>
                                        <li>
                                            <button type="button">Edit Report</button>
                                        </li>
                                        <li>
                                            <button type="button">Mark as Done</button>
                                        </li>
                                    </ul>
                                </Dropdown>
                            </div>
                        </div>
                        <div>
                            <div className="space-y-6">
                                <div className="flex">
                                    <span className="place-content-center grid bg-success-light dark:bg-success rounded-md w-9 h-9 text-base text-success dark:text-success-light shrink-0">SP</span>
                                    <div className="flex-1 px-3">
                                        <div>Shaun Park</div>
                                        <div className="text-white-dark text-xs dark:text-gray-500">10 Jan 1:00PM</div>
                                    </div>
                                    <span className="rtl:mr-auto ltr:ml-auto px-1 text-base text-success whitespace-pre">+$36.11</span>
                                </div>
                                <div className="flex">
                                    <span className="place-content-center grid bg-warning-light dark:bg-warning rounded-md w-9 h-9 text-warning dark:text-warning-light shrink-0">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M2 10C2 7.17157 2 5.75736 2.87868 4.87868C3.75736 4 5.17157 4 8 4H13C15.8284 4 17.2426 4 18.1213 4.87868C19 5.75736 19 7.17157 19 10C19 12.8284 19 14.2426 18.1213 15.1213C17.2426 16 15.8284 16 13 16H8C5.17157 16 3.75736 16 2.87868 15.1213C2 14.2426 2 12.8284 2 10Z"
                                                stroke="currentColor"
                                                strokeWidth="1.5"
                                            />
                                            <path
                                                opacity="0.5"
                                                d="M19.0003 7.07617C19.9754 7.17208 20.6317 7.38885 21.1216 7.87873C22.0003 8.75741 22.0003 10.1716 22.0003 13.0001C22.0003 15.8285 22.0003 17.2427 21.1216 18.1214C20.2429 19.0001 18.8287 19.0001 16.0003 19.0001H11.0003C8.17187 19.0001 6.75766 19.0001 5.87898 18.1214C5.38909 17.6315 5.17233 16.9751 5.07642 16"
                                                stroke="currentColor"
                                                strokeWidth="1.5"
                                            />
                                            <path
                                                d="M13 10C13 11.3807 11.8807 12.5 10.5 12.5C9.11929 12.5 8 11.3807 8 10C8 8.61929 9.11929 7.5 10.5 7.5C11.8807 7.5 13 8.61929 13 10Z"
                                                stroke="currentColor"
                                                strokeWidth="1.5"
                                            />
                                            <path opacity="0.5" d="M16 12L16 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                            <path opacity="0.5" d="M5 12L5 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                        </svg>
                                    </span>
                                    <div className="flex-1 px-3">
                                        <div>Cash withdrawal</div>
                                        <div className="text-white-dark text-xs dark:text-gray-500">04 Jan 1:00PM</div>
                                    </div>
                                    <span className="rtl:mr-auto ltr:ml-auto px-1 text-base text-danger whitespace-pre">-$16.44</span>
                                </div>
                                <div className="flex">
                                    <span className="place-content-center grid bg-danger-light dark:bg-danger rounded-md w-9 h-9 text-danger dark:text-danger-light shrink-0">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="6" r="4" stroke="currentColor" strokeWidth="1.5" />
                                            <path
                                                opacity="0.5"
                                                d="M20 17.5C20 19.9853 20 22 12 22C4 22 4 19.9853 4 17.5C4 15.0147 7.58172 13 12 13C16.4183 13 20 15.0147 20 17.5Z"
                                                stroke="currentColor"
                                                strokeWidth="1.5"
                                            />
                                        </svg>
                                    </span>
                                    <div className="flex-1 px-3">
                                        <div>Amy Diaz</div>
                                        <div className="text-white-dark text-xs dark:text-gray-500">10 Jan 1:00PM</div>
                                    </div>
                                    <span className="rtl:mr-auto ltr:ml-auto px-1 text-base text-success whitespace-pre">+$66.44</span>
                                </div>
                                <div className="flex">
                                    <span className="place-content-center grid bg-secondary-light dark:bg-secondary rounded-md w-9 h-9 text-secondary dark:text-secondary-light shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24">
                                            <path
                                                fill="currentColor"
                                                d="M5.398 0v.006c3.028 8.556 5.37 15.175 8.348 23.596c2.344.058 4.85.398 4.854.398c-2.8-7.924-5.923-16.747-8.487-24zm8.489 0v9.63L18.6 22.951c-.043-7.86-.004-15.913.002-22.95zM5.398 1.05V24c1.873-.225 2.81-.312 4.715-.398v-9.22z"
                                            />
                                        </svg>
                                    </span>
                                    <div className="flex-1 px-3">
                                        <div>Netflix</div>
                                        <div className="text-white-dark text-xs dark:text-gray-500">04 Jan 1:00PM</div>
                                    </div>
                                    <span className="rtl:mr-auto ltr:ml-auto px-1 text-base text-danger whitespace-pre">-$32.00</span>
                                </div>
                                <div className="flex">
                                    <span className="place-content-center grid bg-info-light dark:bg-info rounded-md w-9 h-9 text-base text-info dark:text-info-light shrink-0">DA</span>
                                    <div className="flex-1 px-3">
                                        <div>Daisy Anderson</div>
                                        <div className="text-white-dark text-xs dark:text-gray-500">10 Jan 1:00PM</div>
                                    </div>
                                    <span className="rtl:mr-auto ltr:ml-auto px-1 text-base text-success whitespace-pre">+$10.08</span>
                                </div>
                                <div className="flex">
                                    <span className="place-content-center grid bg-primary-light dark:bg-primary rounded-md w-9 h-9 text-primary dark:text-primary-light shrink-0">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M13.926 9.70541C13.5474 9.33386 13.5474 8.74151 13.5474 7.55682V7.24712C13.5474 3.96249 13.5474 2.32018 12.6241 2.03721C11.7007 1.75425 10.711 3.09327 8.73167 5.77133L5.66953 9.91436C4.3848 11.6526 3.74244 12.5217 4.09639 13.205C4.10225 13.2164 4.10829 13.2276 4.1145 13.2387C4.48945 13.9117 5.59888 13.9117 7.81775 13.9117C9.05079 13.9117 9.6673 13.9117 10.054 14.2754"
                                                stroke="currentColor"
                                                strokeWidth="1.5"
                                            />
                                            <path
                                                opacity="0.5"
                                                d="M13.9259 9.70557L13.9459 9.72481C14.3326 10.0885 14.9492 10.0885 16.1822 10.0885C18.4011 10.0885 19.5105 10.0885 19.8854 10.7615C19.8917 10.7726 19.8977 10.7838 19.9036 10.7951C20.2575 11.4785 19.6151 12.3476 18.3304 14.0858L15.2682 18.2288C13.2888 20.9069 12.2991 22.2459 11.3758 21.9629C10.4524 21.68 10.4524 20.0376 10.4525 16.753L10.4525 16.4434C10.4525 15.2587 10.4525 14.6663 10.074 14.2948L10.054 14.2755"
                                                stroke="currentColor"
                                                strokeWidth="1.5"
                                            />
                                        </svg>
                                    </span>
                                    <div className="flex-1 px-3">
                                        <div>Electricity Bill</div>
                                        <div className="text-white-dark text-xs dark:text-gray-500">04 Jan 1:00PM</div>
                                    </div>
                                    <span className="rtl:mr-auto ltr:ml-auto px-1 text-base text-danger whitespace-pre">-$22.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="p-0 border-0 h-full overflow-hidden panel">
                        <div className="bg-gradient-to-r from-[#4361ee] to-[#160f6b] p-6 min-h-[190px]">
                            <div className="flex justify-between items-center mb-6">
                                <div className="flex items-center bg-black/50 p-1 ltr:pr-3 rtl:pl-3 rounded-full font-semibold text-white">
                                    <img className="block ltr:mr-1 rtl:ml-1 border-2 border-white/50 rounded-full w-8 h-8 object-cover" src="/assets/images/profile-34.jpeg" alt="avatar" />
                                    Alan Green
                                </div>
                                <button type="button" className="flex justify-between items-center bg-black hover:opacity-80 rtl:mr-auto ltr:ml-auto rounded-md w-9 h-9 text-white">
                                    <svg className="m-auto w-6 h-6" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                </button>
                            </div>
                            <div className="flex justify-between items-center text-white">
                                <p className="text-xl">Wallet Balance</p>
                                <h5 className="rtl:mr-auto ltr:ml-auto text-2xl">
                                    <span className="text-white-light">$</span>2953
                                </h5>
                            </div>
                        </div>
                        <div className="gap-2 grid grid-cols-2 -mt-12 px-8">
                            <div className="bg-white dark:bg-[#060818] shadow px-4 py-2.5 rounded-md">
                                <span className="flex justify-between items-center mb-4 dark:text-white">
                                    Received
                                    <svg className="w-4 h-4 text-success" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M19 15L12 9L5 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                </span>
                                <div className="bg-[#ebedf2] dark:bg-black shadow-none py-1 border-0 w-full text-[#515365] text-base dark:text-[#bfc9d4] btn">$97.99</div>
                            </div>
                            <div className="bg-white dark:bg-[#060818] shadow px-4 py-2.5 rounded-md">
                                <span className="flex justify-between items-center mb-4 dark:text-white">
                                    Spent
                                    <svg className="w-4 h-4 text-danger" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M19 9L12 15L5 9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                </span>
                                <div className="bg-[#ebedf2] dark:bg-black shadow-none py-1 border-0 w-full text-[#515365] text-base dark:text-[#bfc9d4] btn">$53.00</div>
                            </div>
                        </div>
                        <div className="p-5">
                            <div className="mb-5">
                                <span className="before:inline-block bg-[#1b2e4b] before:bg-white ltr:before:mr-2 rtl:before:ml-2 px-4 py-1.5 rounded-full before:rounded-full before:w-1.5 before:h-1.5 text-white text-xs">
                                    Pending
                                </span>
                            </div>
                            <div className="space-y-1 mb-5">
                                <div className="flex justify-between items-center">
                                    <p className="font-semibold text-[#515365]">Netflix</p>
                                    <p className="text-base">
                                        <span>$</span> <span className="font-semibold">13.85</span>
                                    </p>
                                </div>
                                <div className="flex justify-between items-center">
                                    <p className="font-semibold text-[#515365]">BlueHost VPN</p>
                                    <p className="text-base">
                                        <span>$</span> <span className="font-semibold">15.66</span>
                                    </p>
                                </div>
                            </div>
                            <div className="flex justify-around px-2 text-center">
                                <button type="button" className="ltr:mr-2 rtl:ml-2 btn btn-secondary">
                                    View Details
                                </button>
                                <button type="button" className="btn btn-success">
                                    Pay Now $29.51
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="gap-6 grid grid-cols-1 lg:grid-cols-2">
                    <div className="w-full h-full panel">
                        <div className="flex justify-between items-center mb-5">
                            <h5 className="font-semibold text-lg dark:text-white-light">Recent Orders</h5>
                        </div>
                        <div className="table-responsive">
                            <table>
                                <thead>
                                    <tr>
                                        <th className="ltr:rounded-l-md rtl:rounded-r-md">Customer</th>
                                        <th>Product</th>
                                        <th>Invoice</th>
                                        <th>Price</th>
                                        <th className="ltr:rounded-r-md rtl:rounded-l-md">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr className="group text-white-dark hover:text-black dark:hover:text-white-light/90">
                                        <td className="min-w-[150px] text-black dark:text-white">
                                            <div className="flex items-center">
                                                <img className="ltr:mr-3 rtl:ml-3 rounded-md w-8 h-8 object-cover" src="/assets/images/profile-6.jpeg" alt="avatar" />
                                                <span className="whitespace-nowrap">Luke Ivory</span>
                                            </div>
                                        </td>
                                        <td className="text-primary">Headphone</td>
                                        <td>
                                            <Link href="/apps/invoice/preview">#46894</Link>
                                        </td>
                                        <td>$56.07</td>
                                        <td>
                                            <span className="bg-success dark:group-hover:bg-transparent shadow-md badge">Paid</span>
                                        </td>
                                    </tr>
                                    <tr className="group text-white-dark hover:text-black dark:hover:text-white-light/90">
                                        <td className="text-black dark:text-white">
                                            <div className="flex items-center">
                                                <img className="ltr:mr-3 rtl:ml-3 rounded-md w-8 h-8 object-cover" src="/assets/images/profile-7.jpeg" alt="avatar" />
                                                <span className="whitespace-nowrap">Andy King</span>
                                            </div>
                                        </td>
                                        <td className="text-info">Nike Sport</td>
                                        <td>
                                            <Link href="/apps/invoice/preview">#76894</Link>
                                        </td>
                                        <td>$126.04</td>
                                        <td>
                                            <span className="bg-secondary dark:group-hover:bg-transparent shadow-md badge">Shipped</span>
                                        </td>
                                    </tr>
                                    <tr className="group text-white-dark hover:text-black dark:hover:text-white-light/90">
                                        <td className="text-black dark:text-white">
                                            <div className="flex items-center">
                                                <img className="ltr:mr-3 rtl:ml-3 rounded-md w-8 h-8 object-cover" src="/assets/images/profile-8.jpeg" alt="avatar" />
                                                <span className="whitespace-nowrap">Laurie Fox</span>
                                            </div>
                                        </td>
                                        <td className="text-warning">Sunglasses</td>
                                        <td>
                                            <Link href="/apps/invoice/preview">#66894</Link>
                                        </td>
                                        <td>$56.07</td>
                                        <td>
                                            <span className="bg-success dark:group-hover:bg-transparent shadow-md badge">Paid</span>
                                        </td>
                                    </tr>
                                    <tr className="group text-white-dark hover:text-black dark:hover:text-white-light/90">
                                        <td className="text-black dark:text-white">
                                            <div className="flex items-center">
                                                <img className="ltr:mr-3 rtl:ml-3 rounded-md w-8 h-8 object-cover" src="/assets/images/profile-9.jpeg" alt="avatar" />
                                                <span className="whitespace-nowrap">Ryan Collins</span>
                                            </div>
                                        </td>
                                        <td className="text-danger">Sport</td>
                                        <td>
                                            <Link href="/apps/invoice/preview">#75844</Link>
                                        </td>
                                        <td>$110.00</td>
                                        <td>
                                            <span className="bg-secondary dark:group-hover:bg-transparent shadow-md badge">Shipped</span>
                                        </td>
                                    </tr>
                                    <tr className="group text-white-dark hover:text-black dark:hover:text-white-light/90">
                                        <td className="text-black dark:text-white">
                                            <div className="flex items-center">
                                                <img className="ltr:mr-3 rtl:ml-3 rounded-md w-8 h-8 object-cover" src="/assets/images/profile-10.jpeg" alt="avatar" />
                                                <span className="whitespace-nowrap">Irene Collins</span>
                                            </div>
                                        </td>
                                        <td className="text-secondary">Speakers</td>
                                        <td>
                                            <Link href="/apps/invoice/preview">#46894</Link>
                                        </td>
                                        <td>$56.07</td>
                                        <td>
                                            <span className="bg-success dark:group-hover:bg-transparent shadow-md badge">Paid</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div className="w-full h-full panel">
                        <div className="flex justify-between items-center mb-5">
                            <h5 className="font-semibold text-lg dark:text-white-light">Top Selling Product</h5>
                        </div>
                        <div className="table-responsive">
                            <table>
                                <thead>
                                    <tr className="border-b-0">
                                        <th className="ltr:rounded-l-md rtl:rounded-r-md">Product</th>
                                        <th>Price</th>
                                        <th>Discount</th>
                                        <th>Sold</th>
                                        <th className="ltr:rounded-r-md rtl:rounded-l-md">Source</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr className="group text-white-dark hover:text-black dark:hover:text-white-light/90">
                                        <td className="min-w-[150px] text-black dark:text-white">
                                            <div className="flex">
                                                <img className="ltr:mr-3 rtl:ml-3 rounded-md w-8 h-8 object-cover" src="/assets/images/product-headphones.jpg" alt="avatar" />
                                                <p className="whitespace-nowrap">
                                                    Headphone
                                                    <span className="block text-primary text-xs">Digital</span>
                                                </p>
                                            </div>
                                        </td>
                                        <td>$168.09</td>
                                        <td>$60.09</td>
                                        <td>170</td>
                                        <td>
                                            <Link className="flex items-center text-danger" href="/">
                                                <svg className="ltr:mr-1 rtl:ml-1 w-3.5 h-3.5 rtl:rotate-180" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M12.6644 5.47875L16.6367 9.00968C18.2053 10.404 18.9896 11.1012 18.9896 11.9993C18.9896 12.8975 18.2053 13.5946 16.6367 14.989L12.6644 18.5199C11.9484 19.1563 11.5903 19.4746 11.2952 19.342C11 19.2095 11 18.7305 11 17.7725V15.4279C7.4 15.4279 3.5 17.1422 2 19.9993C2 10.8565 7.33333 8.57075 11 8.57075V6.22616C11 5.26817 11 4.78917 11.2952 4.65662C11.5903 4.52407 11.9484 4.8423 12.6644 5.47875Z"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    />
                                                    <path
                                                        opacity="0.5"
                                                        d="M15.5386 4.5L20.7548 9.34362C21.5489 10.081 22.0001 11.1158 22.0001 12.1994C22.0001 13.3418 21.4989 14.4266 20.629 15.1671L15.5386 19.5"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                    />
                                                </svg>
                                                Direct
                                            </Link>
                                        </td>
                                    </tr>
                                    <tr className="group text-white-dark hover:text-black dark:hover:text-white-light/90">
                                        <td className="text-black dark:text-white">
                                            <div className="flex">
                                                <img className="ltr:mr-3 rtl:ml-3 rounded-md w-8 h-8 object-cover" src="/assets/images/product-shoes.jpg" alt="avatar" />
                                                <p className="whitespace-nowrap">
                                                    Shoes <span className="block text-warning text-xs">Faishon</span>
                                                </p>
                                            </div>
                                        </td>
                                        <td>$126.04</td>
                                        <td>$47.09</td>
                                        <td>130</td>
                                        <td>
                                            <Link className="flex items-center text-success" href="/">
                                                <svg className="ltr:mr-1 rtl:ml-1 w-3.5 h-3.5 rtl:rotate-180" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M12.6644 5.47875L16.6367 9.00968C18.2053 10.404 18.9896 11.1012 18.9896 11.9993C18.9896 12.8975 18.2053 13.5946 16.6367 14.989L12.6644 18.5199C11.9484 19.1563 11.5903 19.4746 11.2952 19.342C11 19.2095 11 18.7305 11 17.7725V15.4279C7.4 15.4279 3.5 17.1422 2 19.9993C2 10.8565 7.33333 8.57075 11 8.57075V6.22616C11 5.26817 11 4.78917 11.2952 4.65662C11.5903 4.52407 11.9484 4.8423 12.6644 5.47875Z"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    />
                                                    <path
                                                        opacity="0.5"
                                                        d="M15.5386 4.5L20.7548 9.34362C21.5489 10.081 22.0001 11.1158 22.0001 12.1994C22.0001 13.3418 21.4989 14.4266 20.629 15.1671L15.5386 19.5"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                    />
                                                </svg>
                                                Google
                                            </Link>
                                        </td>
                                    </tr>
                                    <tr className="group text-white-dark hover:text-black dark:hover:text-white-light/90">
                                        <td className="text-black dark:text-white">
                                            <div className="flex">
                                                <img className="ltr:mr-3 rtl:ml-3 rounded-md w-8 h-8 object-cover" src="/assets/images/product-watch.jpg" alt="avatar" />
                                                <p className="whitespace-nowrap">
                                                    Watch <span className="block text-danger text-xs">Accessories</span>
                                                </p>
                                            </div>
                                        </td>
                                        <td>$56.07</td>
                                        <td>$20.00</td>
                                        <td>66</td>
                                        <td>
                                            <Link className="flex items-center text-warning" href="/">
                                                <svg className="ltr:mr-1 rtl:ml-1 w-3.5 h-3.5 rtl:rotate-180" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M12.6644 5.47875L16.6367 9.00968C18.2053 10.404 18.9896 11.1012 18.9896 11.9993C18.9896 12.8975 18.2053 13.5946 16.6367 14.989L12.6644 18.5199C11.9484 19.1563 11.5903 19.4746 11.2952 19.342C11 19.2095 11 18.7305 11 17.7725V15.4279C7.4 15.4279 3.5 17.1422 2 19.9993C2 10.8565 7.33333 8.57075 11 8.57075V6.22616C11 5.26817 11 4.78917 11.2952 4.65662C11.5903 4.52407 11.9484 4.8423 12.6644 5.47875Z"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    />
                                                    <path
                                                        opacity="0.5"
                                                        d="M15.5386 4.5L20.7548 9.34362C21.5489 10.081 22.0001 11.1158 22.0001 12.1994C22.0001 13.3418 21.4989 14.4266 20.629 15.1671L15.5386 19.5"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                    />
                                                </svg>
                                                Ads
                                            </Link>
                                        </td>
                                    </tr>
                                    <tr className="group text-white-dark hover:text-black dark:hover:text-white-light/90">
                                        <td className="text-black dark:text-white">
                                            <div className="flex">
                                                <img className="ltr:mr-3 rtl:ml-3 rounded-md w-8 h-8 object-cover" src="/assets/images/product-laptop.jpg" alt="avatar" />
                                                <p className="whitespace-nowrap">
                                                    Laptop <span className="block text-primary text-xs">Digital</span>
                                                </p>
                                            </div>
                                        </td>
                                        <td>$110.00</td>
                                        <td>$33.00</td>
                                        <td>35</td>
                                        <td>
                                            <Link className="flex items-center text-secondary" href="/">
                                                <svg className="ltr:mr-1 rtl:ml-1 w-3.5 h-3.5 rtl:rotate-180" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M12.6644 5.47875L16.6367 9.00968C18.2053 10.404 18.9896 11.1012 18.9896 11.9993C18.9896 12.8975 18.2053 13.5946 16.6367 14.989L12.6644 18.5199C11.9484 19.1563 11.5903 19.4746 11.2952 19.342C11 19.2095 11 18.7305 11 17.7725V15.4279C7.4 15.4279 3.5 17.1422 2 19.9993C2 10.8565 7.33333 8.57075 11 8.57075V6.22616C11 5.26817 11 4.78917 11.2952 4.65662C11.5903 4.52407 11.9484 4.8423 12.6644 5.47875Z"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    />
                                                    <path
                                                        opacity="0.5"
                                                        d="M15.5386 4.5L20.7548 9.34362C21.5489 10.081 22.0001 11.1158 22.0001 12.1994C22.0001 13.3418 21.4989 14.4266 20.629 15.1671L15.5386 19.5"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                    />
                                                </svg>
                                                Email
                                            </Link>
                                        </td>
                                    </tr>
                                    <tr className="group text-white-dark hover:text-black dark:hover:text-white-light/90">
                                        <td className="text-black dark:text-white">
                                            <div className="flex">
                                                <img className="ltr:mr-3 rtl:ml-3 rounded-md w-8 h-8 object-cover" src="/assets/images/product-camera.jpg" alt="avatar" />
                                                <p className="whitespace-nowrap">
                                                    Camera <span className="block text-primary text-xs">Digital</span>
                                                </p>
                                            </div>
                                        </td>
                                        <td>$56.07</td>
                                        <td>$26.04</td>
                                        <td>30</td>
                                        <td>
                                            <Link className="flex items-center text-primary" href="/">
                                                <svg className="ltr:mr-1 rtl:ml-1 w-3.5 h-3.5 rtl:rotate-180" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M12.6644 5.47875L16.6367 9.00968C18.2053 10.404 18.9896 11.1012 18.9896 11.9993C18.9896 12.8975 18.2053 13.5946 16.6367 14.989L12.6644 18.5199C11.9484 19.1563 11.5903 19.4746 11.2952 19.342C11 19.2095 11 18.7305 11 17.7725V15.4279C7.4 15.4279 3.5 17.1422 2 19.9993C2 10.8565 7.33333 8.57075 11 8.57075V6.22616C11 5.26817 11 4.78917 11.2952 4.65662C11.5903 4.52407 11.9484 4.8423 12.6644 5.47875Z"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    />
                                                    <path
                                                        opacity="0.5"
                                                        d="M15.5386 4.5L20.7548 9.34362C21.5489 10.081 22.0001 11.1158 22.0001 12.1994C22.0001 13.3418 21.4989 14.4266 20.629 15.1671L15.5386 19.5"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                    />
                                                </svg>
                                                Referral
                                            </Link>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Index;



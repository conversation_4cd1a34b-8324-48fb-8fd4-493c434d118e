import React from 'react';
import { createRoot } from 'react-dom/client';
import { createInertiaApp } from '@inertiajs/react';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';

// Perfect Scrollbar
import 'react-perfect-scrollbar/dist/css/styles.css';

// Tailwind css
import './tailwind.css';

// i18n (needs to be bundled)
import './i18n';

// Redux
import { Provider } from 'react-redux';
import store from './store/index';

import InertiaLayout from './layouts/InertiaLayout';

createInertiaApp({
    resolve: (name) => {
        const pages = import.meta.glob('./pages/**/*.tsx', { eager: true });
        const page = pages[`./pages/${name}.tsx`];
        return page;
    },
    setup({ el, App, props }) {
        if (!el) {
            throw new Error('Root element not found');
        }

        const root = createRoot(el);
        root.render(
            <Provider store={store}>
                <App {...props}>
                    {({ Component, props }) => (
                        <InertiaLayout layout={props.layout}>
                            <Component {...props} />
                        </InertiaLayout>
                    )}
                </App>
            </Provider>
        );
    },
});


-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jun 06, 2025 at 08:15 AM
-- Server version: 8.0.30
-- PHP Version: 8.3.15

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `booknblush`
--

-- --------------------------------------------------------

--
-- Table structure for table `appointments`
--

CREATE TABLE `appointments` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `appointment_date` date NOT NULL,
  `appointment_time` time NOT NULL,
  `ticket_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('pending','in_progress','completed','cancelled') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `appointments`
--

INSERT INTO `appointments` (`id`, `user_id`, `branch_id`, `appointment_date`, `appointment_time`, `ticket_number`, `status`, `notes`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 5, 2, '2025-06-04', '13:15:00', 'APT-20250604-889C4', 'pending', 'This is my service listing', '2025-06-04 01:44:37', '2025-06-05 23:38:10', NULL),
(2, 6, 2, '2025-06-04', '14:00:00', 'APT-20250604-B76F6', 'pending', 'customer 2', '2025-06-04 02:03:57', '2025-06-04 08:30:51', NULL),
(3, 7, 2, '2025-06-04', '09:00:00', 'APT-20250604-69316', 'completed', 'malish i want', '2025-06-04 02:04:32', '2025-06-04 08:18:58', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `appointment_services`
--

CREATE TABLE `appointment_services` (
  `id` bigint UNSIGNED NOT NULL,
  `appointment_id` bigint UNSIGNED NOT NULL,
  `service_id` bigint UNSIGNED NOT NULL,
  `price` decimal(8,2) DEFAULT '0.00',
  `service_name` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `seat_id` bigint UNSIGNED DEFAULT NULL,
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `estimated_end_time` timestamp NULL DEFAULT NULL,
  `service_notes` text COLLATE utf8mb4_unicode_ci,
  `status` enum('pending','in_progress','completed','cancelled') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `appointment_services`
--

INSERT INTO `appointment_services` (`id`, `appointment_id`, `service_id`, `price`, `service_name`, `seat_id`, `start_time`, `end_time`, `estimated_end_time`, `service_notes`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 2, 100.00, 'Hair Coloring', 13, NULL, NULL, NULL, NULL, 'pending', '2025-06-04 01:44:37', '2025-06-05 23:38:10', NULL),
(2, 1, 7, 75.00, 'malish', 13, NULL, NULL, NULL, NULL, 'pending', '2025-06-04 01:44:37', '2025-06-05 23:38:10', NULL),
(3, 1, 3, 40.00, 'Styling', 13, NULL, NULL, NULL, NULL, 'pending', '2025-06-04 01:44:37', '2025-06-05 23:38:10', NULL),
(4, 2, 2, 100.00, 'Hair Coloring', 14, NULL, NULL, NULL, NULL, 'pending', '2025-06-04 02:03:57', '2025-06-04 08:30:51', NULL),
(5, 3, 7, 75.00, 'malish', 14, '2025-06-04 08:18:48', '2025-06-04 08:18:58', '2025-06-04 08:48:48', NULL, 'completed', '2025-06-04 02:04:32', '2025-06-04 08:18:58', NULL),
(6, 2, 7, 75.00, 'malish', 14, NULL, NULL, NULL, NULL, 'pending', '2025-06-04 03:00:25', '2025-06-04 08:30:51', NULL),
(7, 2, 3, 40.00, 'Styling', 14, NULL, NULL, NULL, NULL, 'pending', '2025-06-04 03:00:25', '2025-06-04 08:30:51', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `branches`
--

CREATE TABLE `branches` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `logo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `branches`
--

INSERT INTO `branches` (`id`, `user_id`, `name`, `address`, `email`, `phone`, `logo`, `is_active`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 'Main Branch', '123 Main Street', NULL, '9876543210', NULL, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(2, 8, 'sahil', 'Sadhuwaswani road', '<EMAIL>', '2222332232', NULL, 1, '2025-06-02 02:17:21', '2025-06-03 06:27:39', NULL),
(3, 8, 'test', 'Khodiyar nagar main road', '<EMAIL>', '34343343434', NULL, 1, '2025-06-03 04:27:59', '2025-06-03 06:28:27', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `domains`
--

CREATE TABLE `domains` (
  `id` int UNSIGNED NOT NULL,
  `domain` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `domains`
--

INSERT INTO `domains` (`id`, `domain`, `tenant_id`, `created_at`, `updated_at`) VALUES
(1, 'sahil.booknblush.test', 'sahil', '2025-06-02 02:17:21', '2025-06-02 02:17:21');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `queue` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint UNSIGNED NOT NULL,
  `reserved_at` int UNSIGNED DEFAULT NULL,
  `available_at` int UNSIGNED NOT NULL,
  `created_at` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(20, '0001_01_01_000000_create_users_table', 1),
(21, '0001_01_01_000001_create_cache_table', 1),
(22, '0001_01_01_000002_create_jobs_table', 1),
(23, '2019_09_15_000010_create_tenants_table', 1),
(24, '2019_09_15_000020_create_domains_table', 1),
(25, '2025_05_01_000001_create_branches_table', 1),
(26, '2025_05_12_131051_create_permission_tables', 1),
(27, '2025_05_13_080436_create_personal_access_tokens_table', 1),
(28, '2025_05_15_000001_create_services_table', 1),
(29, '2025_05_20_000001_create_seats_table', 1),
(30, '2025_05_20_000002_create_appointments_table', 1),
(31, '2025_05_20_000003_create_appointment_services_table', 1),
(32, '2025_05_20_000005_create_working_hours_table', 1),
(33, '2025_05_26_083825_add_company_domain_to_users_table', 1),
(34, '2025_05_26_093047_add_tenant_user_id_to_users_table', 1),
(35, '2025_05_26_094442_remove_tenant_user_id_from_users_table', 1),
(36, '2025_05_27_000001_create_subscription_plans_table', 1),
(37, '2025_05_27_000002_create_vendor_subscriptions_table', 1),
(38, '2025_06_01_000001_create_terms_conditions_table', 1),
(39, '2024_03_21_000001_create_plans_table', 2),
(40, '2024_03_21_000002_create_plan_services_table', 2);

-- --------------------------------------------------------

--
-- Table structure for table `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(3, 'App\\Models\\User', 2),
(3, 'App\\Models\\User', 3),
(3, 'App\\Models\\User', 4),
(4, 'App\\Models\\User', 5),
(4, 'App\\Models\\User', 6),
(4, 'App\\Models\\User', 7),
(2, 'App\\Models\\User', 8),
(3, 'App\\Models\\User', 9),
(3, 'App\\Models\\User', 11);

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'view branches', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(2, 'create branches', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(3, 'edit branches', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(4, 'delete branches', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(5, 'view staff', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(6, 'create staff', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(7, 'edit staff', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(8, 'delete staff', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(9, 'view services', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(10, 'create services', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(11, 'edit services', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(12, 'delete services', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(13, 'view seats', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(14, 'create seats', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(15, 'edit seats', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(16, 'delete seats', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(17, 'view appointments', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(18, 'create appointments', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(19, 'edit appointments', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(20, 'delete appointments', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(21, 'view terms', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(22, 'create terms', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(23, 'edit terms', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(24, 'delete terms', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(25, 'view vendors', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(26, 'create vendors', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(27, 'edit vendors', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(28, 'delete vendors', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(29, 'view working-hours', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(30, 'edit working-hours', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55');

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plans`
--

CREATE TABLE `plans` (
  `id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `validity_days` int NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plans`
--

INSERT INTO `plans` (`id`, `branch_id`, `name`, `price`, `validity_days`, `description`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 2, 'Gold Package', 1500.00, 30, NULL, 'active', '2025-06-06 02:43:59', '2025-06-06 02:44:10', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `plan_services`
--

CREATE TABLE `plan_services` (
  `id` bigint UNSIGNED NOT NULL,
  `plan_id` bigint UNSIGNED NOT NULL,
  `service_id` bigint UNSIGNED NOT NULL,
  `allowed_count` int NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plan_services`
--

INSERT INTO `plan_services` (`id`, `plan_id`, `service_id`, `allowed_count`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 2, 3, '2025-06-06 02:43:59', '2025-06-06 02:43:59', NULL),
(2, 1, 3, 2, '2025-06-06 02:43:59', '2025-06-06 02:43:59', NULL),
(3, 1, 7, 5, '2025-06-06 02:43:59', '2025-06-06 02:43:59', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'admin', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(2, 'vendor', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(3, 'staff', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(4, 'customer', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55');

-- --------------------------------------------------------

--
-- Table structure for table `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint UNSIGNED NOT NULL,
  `role_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `role_has_permissions`
--

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(1, 1),
(2, 1),
(3, 1),
(4, 1),
(5, 1),
(6, 1),
(7, 1),
(8, 1),
(9, 1),
(10, 1),
(11, 1),
(12, 1),
(13, 1),
(14, 1),
(15, 1),
(16, 1),
(17, 1),
(18, 1),
(19, 1),
(20, 1),
(21, 1),
(22, 1),
(23, 1),
(24, 1),
(25, 1),
(26, 1),
(27, 1),
(28, 1),
(29, 1),
(30, 1),
(1, 2),
(2, 2),
(3, 2),
(4, 2),
(5, 2),
(6, 2),
(7, 2),
(8, 2),
(9, 2),
(10, 2),
(11, 2),
(12, 2),
(13, 2),
(14, 2),
(15, 2),
(16, 2),
(17, 2),
(18, 2),
(19, 2),
(20, 2),
(21, 2),
(22, 2),
(23, 2),
(24, 2),
(29, 2),
(30, 2),
(9, 3),
(13, 3),
(17, 3),
(18, 3),
(19, 3),
(9, 4),
(17, 4),
(18, 4);

-- --------------------------------------------------------

--
-- Table structure for table `seats`
--

CREATE TABLE `seats` (
  `id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `staff_id` bigint UNSIGNED DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `status` enum('available','occupied','cleaning','maintenance') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'available',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `seats`
--

INSERT INTO `seats` (`id`, `branch_id`, `staff_id`, `name`, `notes`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 2, 'Chair 1', NULL, 'available', '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(2, 1, 3, 'Chair 2', NULL, 'available', '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(3, 1, 4, 'Chair 3', NULL, 'occupied', '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(4, 1, NULL, 'Chair 4', NULL, 'maintenance', '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(12, 2, 11, 'A1', 'fdg', 'available', '2025-06-02 05:17:51', '2025-06-03 02:57:20', NULL),
(13, 2, 11, 'A2', '12', 'available', '2025-06-02 05:25:06', '2025-06-04 00:10:21', NULL),
(14, 2, 11, 'A3', '122', 'available', '2025-06-02 05:25:15', '2025-06-04 08:08:37', NULL),
(15, 2, 11, 'A4', '1212', 'maintenance', '2025-06-02 05:25:32', '2025-06-04 00:10:01', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `duration_minutes` int NOT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `branch_id`, `name`, `description`, `duration_minutes`, `price`, `is_active`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 'Haircut', 'Basic haircut service', 30, 25.00, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(2, 2, 'Hair Coloring', 'Full hair coloring service', 90, 100.00, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(3, 2, 'Styling', 'Hair styling for special occasions', 45, 40.00, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(4, 1, 'Facial', 'Basic facial treatment', 60, 50.00, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(5, 1, 'Manicure', 'Basic manicure service', 30, 20.00, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(7, 2, 'malish', 'test', 30, 75.00, 1, '2025-06-03 03:25:50', '2025-06-03 03:25:50', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `subscription_plans`
--

CREATE TABLE `subscription_plans` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `price` decimal(10,2) NOT NULL,
  `billing_cycle` enum('monthly','yearly') COLLATE utf8mb4_unicode_ci NOT NULL,
  `max_services` int NOT NULL DEFAULT '0',
  `max_appointments_per_month` int NOT NULL DEFAULT '0',
  `max_seats` int NOT NULL DEFAULT '0',
  `max_branches` int NOT NULL DEFAULT '1',
  `max_staff` int NOT NULL DEFAULT '0',
  `has_analytics` tinyint(1) NOT NULL DEFAULT '0',
  `has_api_access` tinyint(1) NOT NULL DEFAULT '0',
  `has_custom_branding` tinyint(1) NOT NULL DEFAULT '0',
  `has_priority_support` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `sort_order` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subscription_plans`
--

INSERT INTO `subscription_plans` (`id`, `name`, `description`, `price`, `billing_cycle`, `max_services`, `max_appointments_per_month`, `max_seats`, `max_branches`, `max_staff`, `has_analytics`, `has_api_access`, `has_custom_branding`, `has_priority_support`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'Starter', 'Perfect for small salons just getting started', 2499.00, 'monthly', 10, 100, 3, 1, 2, 0, 0, 0, 0, 1, 1, '2025-06-02 02:11:01', '2025-06-02 02:11:01'),
(2, 'Professional', 'Ideal for growing salons with multiple staff', 4999.00, 'monthly', 25, 500, 8, 2, 10, 1, 0, 0, 0, 1, 2, '2025-06-02 02:11:01', '2025-06-02 02:11:01'),
(3, 'Business', 'For established salons with multiple locations', 8499.00, 'monthly', 50, 1000, 15, 5, 25, 1, 1, 1, 0, 1, 3, '2025-06-02 02:11:01', '2025-06-02 02:11:01'),
(4, 'Enterprise', 'Unlimited everything for large salon chains', 16999.00, 'monthly', 0, 0, 0, 10, 0, 1, 1, 1, 1, 1, 4, '2025-06-02 02:11:01', '2025-06-02 02:11:01'),
(5, 'Starter (Yearly)', 'Perfect for small salons - Save 20% with yearly billing', 23990.00, 'yearly', 10, 100, 3, 1, 2, 0, 0, 0, 0, 1, 5, '2025-06-02 02:11:01', '2025-06-02 02:11:01'),
(6, 'Professional (Yearly)', 'Ideal for growing salons - Save 20% with yearly billing', 47990.00, 'yearly', 25, 500, 8, 2, 10, 1, 0, 0, 0, 1, 6, '2025-06-02 02:11:01', '2025-06-02 02:11:01');

-- --------------------------------------------------------

--
-- Table structure for table `tenants`
--

CREATE TABLE `tenants` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `data` json DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `tenants`
--

INSERT INTO `tenants` (`id`, `created_at`, `updated_at`, `data`) VALUES
('sahil', '2025-06-02 02:17:20', '2025-06-02 02:17:20', '{\"created_at\": \"2025-06-02 07:47:20\", \"updated_at\": \"2025-06-02 07:47:20\", \"tenancy_db_name\": \"sahil\"}');

-- --------------------------------------------------------

--
-- Table structure for table `terms_conditions`
--

CREATE TABLE `terms_conditions` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `condition` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `terms_conditions`
--

INSERT INTO `terms_conditions` (`id`, `user_id`, `branch_id`, `title`, `condition`, `is_default`, `is_active`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 2, 'Standard Terms', 'These are the standard terms and conditions for our service.', 1, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_domain` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `branch_id` bigint UNSIGNED DEFAULT NULL,
  `current_branch_id` bigint UNSIGNED DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gender` enum('male','female','other') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `company_name`, `company_domain`, `tenant_id`, `branch_id`, `current_branch_id`, `email`, `phone`, `gender`, `address`, `is_active`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'Admin User', NULL, NULL, NULL, 1, 1, '<EMAIL>', '1234567890', 'male', NULL, 1, '2025-06-02 02:10:55', '$2y$10$Rk0SnZxZjhlwOm7KpZUIkO5y4Xrim5uuIRdPMAGYpZA9uiparSJCG', 'IRUA6X6Chs', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(2, 'John Stylist', NULL, NULL, NULL, 1, 1, '<EMAIL>', '1111222233', 'male', NULL, 1, '2025-06-02 02:10:56', '$2y$10$jjFWFFplP0qmwy9wTw0XF.lXJLd6u6l7KeCmv2hKlaJfWlkoZXsn.', 'SzBBd16Fk5', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(3, 'Jane Beautician', NULL, NULL, NULL, 1, 1, '<EMAIL>', '4444555566', 'female', NULL, 1, '2025-06-02 02:10:56', '$2y$10$TUJJEQzR7miTTrh2v9ABfeCZqvXO1InE99kculAufs2ylusQI5yUW', 'eu5MYkDIO5', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(4, 'Mike Barber', NULL, NULL, NULL, 1, 1, '<EMAIL>', '7777888899', 'male', NULL, 1, '2025-06-02 02:10:56', '$2y$10$EI9hiK2LIPgC/OgYwRLBm.Qxw.TFYuKl/GN9hHB91Ij01XX374tmi', '0br1wzUJQ7', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(5, 'Customer One', NULL, NULL, NULL, 1, 1, '<EMAIL>', '1212121212', 'male', NULL, 1, '2025-06-02 02:10:56', '$2y$10$a6me/8OnpwY8uYpBmeTKVOejHubcx7SjMDG3k8/hBTMyIB/YEBQj.', 'BMkgUOnYg2', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(6, 'Customer Two', NULL, NULL, NULL, 1, 1, '<EMAIL>', '2323232323', 'female', NULL, 1, '2025-06-02 02:10:56', '$2y$10$dMXkQxA9.bwWUgL.84O6NeKk/9y2RDIpXJ9eTxpr5E2ZeUpqR0pXy', 'jhuv8fsaVQ', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(7, 'Customer Three', NULL, NULL, NULL, 1, 1, '<EMAIL>', '3434343434', 'other', NULL, 1, '2025-06-02 02:10:56', '$2y$10$9TbpfcZemYHt5iXP6MAMq.EfaOBXTYGdRkhyUSEeuEA5tKgzm4uyC', '49UVoWM0eH', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(8, 'sahil', 'sahil', 'sahil', 'sahil', 2, 2, '<EMAIL>', NULL, NULL, NULL, 1, NULL, '$2y$10$3PFhcCjI1MlKRl5zWtwRSup6LVytNacLspUCrb8hBAImdMiMnnpRK', NULL, '2025-06-02 02:17:21', '2025-06-03 08:28:28'),
(10, 'Rohit staff', NULL, NULL, NULL, 2, 2, '<EMAIL>', '4444555566', 'female', NULL, 1, '2025-06-02 02:10:56', '$2y$10$TUJJEQzR7miTTrh2v9ABfeCZqvXO1InE99kculAufs2ylusQI5yUW', 'eu5MYkDIO5', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(11, 'kishan', NULL, NULL, NULL, 2, 2, '<EMAIL>', '2222332232', 'female', 'kishan kishan', 1, NULL, '$2y$10$YBnDP8JSTwOO/BcB/FR4f.bzxjEyPKSR69O9GHApPAchqCHVwCwtu', NULL, '2025-06-02 05:05:06', '2025-06-02 05:05:06');

-- --------------------------------------------------------

--
-- Table structure for table `vendor_subscriptions`
--

CREATE TABLE `vendor_subscriptions` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `subscription_plan_id` bigint UNSIGNED NOT NULL,
  `starts_at` datetime NOT NULL,
  `ends_at` datetime NOT NULL,
  `trial_ends_at` datetime DEFAULT NULL,
  `status` enum('active','inactive','cancelled','expired','trial') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'trial',
  `current_services_count` int NOT NULL DEFAULT '0',
  `current_appointments_count` int NOT NULL DEFAULT '0',
  `current_seats_count` int NOT NULL DEFAULT '0',
  `current_branches_count` int NOT NULL DEFAULT '0',
  `current_staff_count` int NOT NULL DEFAULT '0',
  `billing_period_start` date DEFAULT NULL,
  `billing_period_end` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `vendor_subscriptions`
--

INSERT INTO `vendor_subscriptions` (`id`, `user_id`, `subscription_plan_id`, `starts_at`, `ends_at`, `trial_ends_at`, `status`, `current_services_count`, `current_appointments_count`, `current_seats_count`, `current_branches_count`, `current_staff_count`, `billing_period_start`, `billing_period_end`, `created_at`, `updated_at`) VALUES
(1, 8, 1, '2025-06-30 23:59:59', '2025-06-16 07:47:21', '2025-06-16 07:47:21', 'trial', 0, 0, 0, 1, 0, '2025-06-01', '2025-06-30', '2025-06-02 02:17:21', '2025-06-02 02:17:21');

-- --------------------------------------------------------

--
-- Table structure for table `working_hours`
--

CREATE TABLE `working_hours` (
  `id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `day` enum('mon','tue','wed','thu','fri','sat','sun') COLLATE utf8mb4_unicode_ci NOT NULL,
  `open_time` time DEFAULT NULL,
  `close_time` time DEFAULT NULL,
  `is_closed` tinyint(1) NOT NULL DEFAULT '0',
  `day_order` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `working_hours`
--

INSERT INTO `working_hours` (`id`, `branch_id`, `day`, `open_time`, `close_time`, `is_closed`, `day_order`, `created_at`, `updated_at`) VALUES
(1, 2, 'mon', '09:00:00', '18:00:00', 0, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(2, 2, 'tue', '09:00:00', '18:00:00', 0, 2, '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(3, 2, 'wed', '09:00:00', '18:00:00', 0, 3, '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(4, 2, 'thu', '09:00:00', '18:00:00', 0, 4, '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(5, 2, 'fri', '09:00:00', '18:00:00', 0, 5, '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(6, 2, 'sat', '09:00:00', '16:00:00', 0, 6, '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(7, 2, 'sun', '09:00:00', '16:00:00', 1, 7, '2025-06-02 02:10:56', '2025-06-02 02:10:56');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `appointments`
--
ALTER TABLE `appointments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `appointments_user_id_foreign` (`user_id`),
  ADD KEY `appointments_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `appointment_services`
--
ALTER TABLE `appointment_services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `appointment_services_appointment_id_foreign` (`appointment_id`),
  ADD KEY `appointment_services_service_id_foreign` (`service_id`),
  ADD KEY `appointment_services_seat_id_foreign` (`seat_id`);

--
-- Indexes for table `branches`
--
ALTER TABLE `branches`
  ADD PRIMARY KEY (`id`),
  ADD KEY `branches_user_id_foreign` (`user_id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `domains`
--
ALTER TABLE `domains`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `domains_domain_unique` (`domain`),
  ADD KEY `domains_tenant_id_foreign` (`tenant_id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `plans`
--
ALTER TABLE `plans`
  ADD PRIMARY KEY (`id`),
  ADD KEY `plans_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `plan_services`
--
ALTER TABLE `plan_services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `plan_services_plan_id_foreign` (`plan_id`),
  ADD KEY `plan_services_service_id_foreign` (`service_id`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Indexes for table `seats`
--
ALTER TABLE `seats`
  ADD PRIMARY KEY (`id`),
  ADD KEY `seats_branch_id_foreign` (`branch_id`),
  ADD KEY `seats_staff_id_foreign` (`staff_id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `services_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tenants`
--
ALTER TABLE `tenants`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `terms_conditions`
--
ALTER TABLE `terms_conditions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `terms_conditions_user_id_foreign` (`user_id`),
  ADD KEY `terms_conditions_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD UNIQUE KEY `users_company_domain_unique` (`company_domain`);

--
-- Indexes for table `vendor_subscriptions`
--
ALTER TABLE `vendor_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_active_subscription` (`user_id`,`status`),
  ADD KEY `vendor_subscriptions_subscription_plan_id_foreign` (`subscription_plan_id`);

--
-- Indexes for table `working_hours`
--
ALTER TABLE `working_hours`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `working_hours_branch_id_day_unique` (`branch_id`,`day`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `appointments`
--
ALTER TABLE `appointments`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `appointment_services`
--
ALTER TABLE `appointment_services`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `branches`
--
ALTER TABLE `branches`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `domains`
--
ALTER TABLE `domains`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=41;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `plans`
--
ALTER TABLE `plans`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `plan_services`
--
ALTER TABLE `plan_services`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `seats`
--
ALTER TABLE `seats`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `services`
--
ALTER TABLE `services`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `terms_conditions`
--
ALTER TABLE `terms_conditions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `vendor_subscriptions`
--
ALTER TABLE `vendor_subscriptions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `working_hours`
--
ALTER TABLE `working_hours`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `appointments`
--
ALTER TABLE `appointments`
  ADD CONSTRAINT `appointments_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `appointments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `appointment_services`
--
ALTER TABLE `appointment_services`
  ADD CONSTRAINT `appointment_services_appointment_id_foreign` FOREIGN KEY (`appointment_id`) REFERENCES `appointments` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `appointment_services_seat_id_foreign` FOREIGN KEY (`seat_id`) REFERENCES `seats` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `appointment_services_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `branches`
--
ALTER TABLE `branches`
  ADD CONSTRAINT `branches_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `domains`
--
ALTER TABLE `domains`
  ADD CONSTRAINT `domains_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `plans`
--
ALTER TABLE `plans`
  ADD CONSTRAINT `plans_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `plan_services`
--
ALTER TABLE `plan_services`
  ADD CONSTRAINT `plan_services_plan_id_foreign` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `plan_services_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `seats`
--
ALTER TABLE `seats`
  ADD CONSTRAINT `seats_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `seats_staff_id_foreign` FOREIGN KEY (`staff_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `services`
--
ALTER TABLE `services`
  ADD CONSTRAINT `services_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`);

--
-- Constraints for table `terms_conditions`
--
ALTER TABLE `terms_conditions`
  ADD CONSTRAINT `terms_conditions_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`),
  ADD CONSTRAINT `terms_conditions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `vendor_subscriptions`
--
ALTER TABLE `vendor_subscriptions`
  ADD CONSTRAINT `vendor_subscriptions_subscription_plan_id_foreign` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `vendor_subscriptions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `working_hours`
--
ALTER TABLE `working_hours`
  ADD CONSTRAINT `working_hours_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build && vite build --ssr"}, "dependencies": {"@emotion/react": "^11.10.6", "@fortawesome/free-regular-svg-icons": "^6.2.0", "@fortawesome/react-fontawesome": "^0.2.0", "@fullcalendar/core": "^6.0.0", "@fullcalendar/daygrid": "^6.0.0", "@fullcalendar/interaction": "^6.0.0", "@fullcalendar/react": "^6.0.0", "@fullcalendar/timegrid": "^6.0.0", "@inertiajs/react": "^2.0.11", "@mantine/core": "^5.10.5", "@mantine/hooks": "^5.10.5", "@popperjs/core": "^2.11.6", "@reduxjs/toolkit": "^2.2.7", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@tippyjs/react": "^4.2.6", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.17", "@types/node": "^22.4.0", "@types/react": "^18.0.21", "@types/react-dom": "^18.0.6", "@types/react-text-mask": "^5.4.11", "@x1mrdonut1x/nouislider-react": "^3.4.3", "apexcharts": "^3.37.1", "easymde": "^2.18.0", "file-upload-with-preview": "^5.0.8", "formik": "^2.2.9", "highlight.js": "^11.6.0", "lodash": "^4.17.21", "lucide-react": "^0.512.0", "mantine-datatable": "^1.7.17", "nouislider-react": "^3.4.1", "react": "^18.2.0", "react-18-image-lightbox": "^5.1.4", "react-apexcharts": "^1.4.0", "react-click-away-listener": "^2.2.2", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.3.2", "react-dom": "^18.2.0", "react-export-table-to-excel": "^1.0.6", "react-flatpickr": "^3.10.13", "react-icons": "^5.5.0", "react-images-uploading": "^3.1.7", "react-popper": "^2.3.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.4.2", "react-scripts": "5.0.1", "react-select": "^5.5.8", "react-simplemde-editor": "^5.2.0", "react-sortablejs": "^6.1.4", "react-text-mask": "^5.5.0", "sweetalert2": "^11.6.8", "sweetalert2-react-content": "^5.0.7", "swiper": "^11.1.9", "web-vitals": "^4.2.3", "yup": "^1.4.0"}, "devDependencies": {"@headlessui/react": "^2.1.2", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "@types/react": "^18.0.27", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "^18.0.10", "@types/react-flatpickr": "^3.8.8", "@types/react-input-mask": "^3.0.1", "@types/styled-components": "^5.1.26", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.17", "i18next": "^23.13.0", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "laravel-vite-plugin": "^1.0.5", "postcss": "^8.4.35", "react-animate-height": "^3.0.4", "react-i18next": "^15.0.1", "react-perfect-scrollbar": "^1.5.8", "tailwindcss": "^3.4.1", "typescript": "^4.8.4", "vite": "^5.4.1"}}
<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AuthController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Dashboard Routes
|--------------------------------------------------------------------------
| Routes for main dashboard pages and admin interfaces
*/

Route::get('/', function () {
    return Inertia::render('Pages/ComingSoonBoxed', [
        'layout' => 'blank'
    ]);
})->name('add');

// Main Dashboard - Protected routes
Route::middleware(['auth'])->group(function () {
    //Route::get('/dashboard', [DashboardController::class, 'index'])->name('home');
    Route::get('/salon-admin', [DashboardController::class, 'salonAdmin'])->name('salon-admin');
    Route::get('/user-admin', [DashboardController::class, 'userAdmin'])->name('user-admin');
    Route::get('/super-admin', [DashboardController::class, 'superAdmin'])->name('super-admin');
    Route::get('/analytics', [DashboardController::class, 'analytics'])->name('analytics');
    Route::get('/finance', [DashboardController::class, 'finance'])->name('finance');

    // User Profile & Settings
    Route::get('/users/profile', function () {
        return Inertia::render('Users/Profile');
    })->name('users.profile');

    Route::get('/users/account-setting', function () {
        return Inertia::render('Users/AccountSetting');
    })->name('users.account-setting');

    // Apps
    Route::get('/apps/calendar', function () {
        return Inertia::render('Apps/Calendar');
    })->name('apps.calendar');

    Route::get('/apps/chat', function () {
        return Inertia::render('Apps/Chat');
    })->name('apps.chat');

    Route::get('/apps/contacts', function () {
        return Inertia::render('Apps/Contacts');
    })->name('apps.contacts');

    // Invoice Management
    Route::prefix('apps/invoice')->name('apps.invoice.')->group(function () {
        Route::get('/list', function () {
            return Inertia::render('Apps/Invoice/List');
        })->name('list');

        Route::get('/preview', function () {
            return Inertia::render('Apps/Invoice/Preview');
        })->name('preview');

        Route::get('/add', function () {
            return Inertia::render('Apps/Invoice/Add');
        })->name('add');

        Route::get('/edit', function () {
            return Inertia::render('Apps/Invoice/Edit');
        })->name('edit');
    });

});

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
| Routes for user authentication and account management
*/

// Guest-only routes
Route::middleware(['guest'])->group(function () {
    // Authentication Routes
    Route::get('/auth/login', [App\Http\Controllers\Auth\AuthenticatedSessionController::class, 'create'])
        ->name('login');
    Route::post('/auth/login', [App\Http\Controllers\Auth\AuthenticatedSessionController::class, 'store']);

    Route::middleware(['central'])->group(function () {
        Route::get('/auth/register', [App\Http\Controllers\Auth\RegisteredUserController::class, 'create'])
            ->name('register');
        Route::post('/auth/register', [App\Http\Controllers\Auth\RegisteredUserController::class, 'store']);
    });


});

// Logout route (for authenticated users)
Route::post('/auth/logout', [App\Http\Controllers\Auth\AuthenticatedSessionController::class, 'destroy'])
    ->middleware('auth')
    ->name('logout');

// Vendor redirect route
Route::get('/vendor/redirect', function () {
    $url = session('vendor_redirect_url');
    return Inertia::render('VendorRedirect', [
        'redirectUrl' => $url,
        'layout' => 'blank'
    ]);
})->name('vendor.redirect');

/*
|--------------------------------------------------------------------------
| User Management Routes
|--------------------------------------------------------------------------
| Routes for user profiles and account settings
*/

// User Profile & Settings
Route::get('/users/profile', function () {
    return Inertia::render('Users/Profile');
})->name('users.profile');

Route::get('/users/account-setting', function () {
    return Inertia::render('Users/AccountSetting');
})->name('users.account-setting');

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
| Routes for main application features and tools
*/

// Apps
Route::get('/apps/calendar', function () {
    return Inertia::render('Apps/Calendar');
})->name('apps.calendar');

Route::get('/apps/chat', function () {
    return Inertia::render('Apps/Chat');
})->name('apps.chat');

Route::get('/apps/contacts', function () {
    return Inertia::render('Apps/Contacts');
})->name('apps.contacts');

// Invoice Management
Route::get('/apps/invoice/list', function () {
    return Inertia::render('Apps/Invoice/List');
})->name('apps.invoice.list');

Route::get('/apps/invoice/preview', function () {
    return Inertia::render('Apps/Invoice/Preview');
})->name('apps.invoice.preview');

Route::get('/apps/invoice/add', function () {
    return Inertia::render('Apps/Invoice/Add');
})->name('apps.invoice.add');

Route::get('/apps/invoice/edit', function () {
    return Inertia::render('Apps/Invoice/Edit');
})->name('apps.invoice.edit');

/*
|--------------------------------------------------------------------------
| Catch-All Route
|--------------------------------------------------------------------------
| This route handles any undefined paths
*/

// Temporarily disabled catch-all route to test appointment routes
// Route::get('/{path}', function ($path) {
//     // Try to render the page if it exists
//     try {
//         return Inertia::render($path);
//     } catch (\Exception $e) {
//         // If the page doesn't exist, redirect to home
//         return redirect()->route('home');
//     }
// })->where('path', '^(?!appointment).*');


